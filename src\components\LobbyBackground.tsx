import React, { useEffect, useRef } from 'react';
import { drawPellets } from './Game/rendering/drawPellets';
import type { DisplayPellet } from './Game/utils';
import { createOrganicState, updateOrganicState, type OrganicShapeState } from './Game/rendering/drawOrganicShape';


const LobbyBackground: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | undefined>(undefined);
  const pelletsRef = useRef<DisplayPellet[]>([]);
  const organicStatesRef = useRef<Map<string, OrganicShapeState>>(new Map());
  const lastTimeRef = useRef<number>(0);

  // Initialize pellets and players
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const width = rect.width;
    const height = rect.height;

    // Create animated pellets scattered around
    const pellets: DisplayPellet[] = [];
    const organicStates = new Map<string, OrganicShapeState>();
    
    for (let i = 0; i < 80; i++) {
      const id = `pellet_${i}`;
      
      pellets.push({
        id,
        x: Math.random() * width,
        y: Math.random() * height,
        mass: 2, // 10-25 mass for variety
      });
      
      // Create organic state for each pellet
      organicStates.set(id, createOrganicState());
    }

    pelletsRef.current = pellets;
    organicStatesRef.current = organicStates;
  }, []);

  // Animation loop
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const animate = (currentTime: number = 0) => {
      const rect = canvas.getBoundingClientRect();
      const width = rect.width;
      const height = rect.height;
      
      // Calculate delta time
      const dt = lastTimeRef.current ? (currentTime - lastTimeRef.current) / 1000 : 0;
      lastTimeRef.current = currentTime;
      
      // Update canvas size if needed
      if (canvas.width !== width || canvas.height !== height) {
        canvas.width = width;
        canvas.height = height;
      }

      // Clear canvas with dark background
      ctx.fillStyle = '#0a0a0a';
      ctx.fillRect(0, 0, width, height);

      // Draw grid pattern (more visible like in agar.io)
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.08)';
      ctx.lineWidth = 1;
      const gridSize = 50;
      
      for (let x = 0; x < width; x += gridSize) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
      }
      
      for (let y = 0; y < height; y += gridSize) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
      }

      // Update organic states
      organicStatesRef.current.forEach(state => {
        updateOrganicState(state, dt);
      });

      // Draw pellets using the game's pellet rendering system
      // We use a fixed camera position (center of canvas) and scale of 1
      drawPellets(
        ctx,
        pelletsRef.current,
        width / 2, // camX - center of canvas
        height / 2, // camY - center of canvas
        width,
        height,
        1, // scale
        width / 2, // worldHalfW
        height / 2, // worldHalfH
        organicStatesRef.current,
        false // showMass
      );

      animationRef.current = requestAnimationFrame(animate);
    };

    animate(performance.now());

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      const canvas = canvasRef.current;
      if (!canvas) return;
      
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width;
      canvas.height = rect.height;
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Initial size

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 w-full h-full pointer-events-none"
      style={{ zIndex: 1 }}
    />
  );
};

export default LobbyBackground;