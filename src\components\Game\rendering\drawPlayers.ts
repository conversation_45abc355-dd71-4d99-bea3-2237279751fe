import { worldToScreen, lamportsToSol } from "../utils";
import type { DisplayPlayer } from "../utils";
import { CONFIG, massToRadius as sharedMassToRadius } from "@/shared/game-config";
import { darkenColor } from "./blobStyles";
import { drawOrganicShape, OrganicShapeState } from "./drawOrganicShape";
import type { TickMsg, PlayerSnap } from "@/lib/types";

type DrawPlayersOpts = {
  youId: string | null;
  camX: number;
  camY: number;
  width: number;
  height: number;
  scale: number;
  prevPlayersMap: Map<string, PlayerSnap>;
  currPlayersMap: Map<string, PlayerSnap>;
  prevTick: TickMsg | null;
  currTick: TickMsg | null;
  solPriceUsd: number | null;
  organicStates?: Map<string, OrganicShapeState>;
  showMass?: boolean;
};

export function drawPlayers(
  ctx: CanvasRenderingContext2D,
  players: Iterable<DisplayPlayer>,
  opts: DrawPlayersOpts
) {
  const {
    youId,
    camX,
    camY,
    width,
    height,
    scale,
    solPriceUsd,
    organicStates,
    showMass,
    currTick,
  } = opts;

  const worldHalfW = width / (2 * scale);
  const worldHalfH = height / (2 * scale);

  // Convert players to array and sort by rendering priority
  // Bigger blobs should be drawn above smaller ones during merging
  const playersArray = Array.from(players);
  
  // Sort players by mass (ascending) so larger players are drawn last (on top)
  // Also prioritize cells that are absorbing others to be drawn on top
  playersArray.sort((a, b) => {
    const aState = organicStates?.get(a.id);
    const bState = organicStates?.get(b.id);
    
    // Check if either player is involved in absorption
    const aIsAbsorbing = aState?.isAbsorbing || false;
    const bIsAbsorbing = bState?.isAbsorbing || false;
    
    // If one is being absorbed and the other isn't, prioritize the non-absorbed one
    if (aIsAbsorbing && !bIsAbsorbing) return -1; // a (being absorbed) goes first (behind)
    if (!aIsAbsorbing && bIsAbsorbing) return 1;  // b (being absorbed) goes first (behind)
    
    // For cells of the same absorption state, sort by mass (smaller first, larger last/on top)
    return a.mass - b.mass;
  });

  for (const cur of playersArray) {
    // cull offscreen early
    const radiusWorld = sharedMassToRadius(cur.mass);
    const radiusScreen = Math.max(12, radiusWorld * scale);
    if (
      Math.abs(cur.x - camX) > (worldHalfW + radiusScreen / Math.max(0.001, scale) + 30) ||
      Math.abs(cur.y - camY) > (worldHalfH + radiusScreen / Math.max(0.001, scale) + 30)
    ) {
      continue;
    }

    const pos = worldToScreen(cur.x, cur.y, camX, camY, width, height, scale);
    const isYou = !!youId && (cur.id === youId || cur.id.startsWith(`${youId}#`));

    // Get player colors
    const baseColor = cur.color ?? (isYou ? "#7c3aed" : "#3b82f6");
    const strokeColor = darkenColor(baseColor, 30);

    // Keep the inner ring (outline) a constant screen-space width
    const ringW = 8;

    // Visual "scale" factor derived from mass so the whole player scales together
    const visualScale =
      1 + Math.min(
        CONFIG.GROW_CAP,
        Math.pow(cur.mass / CONFIG.GROW_BASE_MASS, CONFIG.GROW_EXPONENT)
      );

    // Get organic state for this player
    const organicState = organicStates?.get(cur.id);
    
    // Create a safe copy of organic state to prevent absorbing cells from shrinking
    // Only absorbed cells should shrink, never the absorbing cell
    let safeOrganicState = organicState;
    if (organicState) {
      // Check if this cell is absorbing another cell (should not shrink)
      const isAbsorbingOthers = currTick?.players?.some(p => (p as any).absorbingInto === cur.id) || false;
      // Check if this cell is being absorbed into another cell (should shrink)
      const isBeingAbsorbed = (cur as any).absorbingInto !== undefined;
      
      // Debug logging for shrinking issues
      if (organicState.isCellMerging || organicState.isAbsorbing) {
        console.log(`[DEBUG] Player ${cur.id} (${cur.name}):`, {
          isCellMerging: organicState.isCellMerging,
          isAbsorbing: organicState.isAbsorbing,
          absorbProgress: organicState.absorbProgress,
          isAbsorbingOthers,
          isBeingAbsorbed,
          absorbingInto: (cur as any).absorbingInto,
          mass: cur.mass
        });
      }
      
      // CRITICAL: Absorbing cells should NEVER shrink
      if (isAbsorbingOthers) {
        // Create a copy without any shrinking effects for absorbing cells
        safeOrganicState = {
          ...organicState,
          isCellMerging: false,
          isAbsorbing: false,
          absorbProgress: 0
        };
        
        // Also clear the original state to prevent future issues
        if (organicState.isCellMerging || organicState.isAbsorbing) {
          console.log(`[DEBUG] Clearing shrinking state from absorbing cell ${cur.id}`);
          organicState.isCellMerging = false;
          organicState.isAbsorbing = false;
          organicState.absorbProgress = 0;
        }
      } else if (!isBeingAbsorbed && (organicState.isCellMerging || organicState.isAbsorbing)) {
        // If cell is not being absorbed but has absorption state, clear it
        console.log(`[DEBUG] Clearing orphaned absorption state from cell ${cur.id}`);
        organicState.isCellMerging = false;
        organicState.isAbsorbing = false;
        organicState.absorbProgress = 0;
        safeOrganicState = organicState;
      }
    }

    // draw scaled group
    ctx.save();
    ctx.translate(pos.x, pos.y);
    ctx.scale(visualScale, visualScale);

    // Draw organic shape at origin with stroke outline
    drawOrganicShape(
      ctx,
      0,
      0,
      radiusScreen / visualScale,
      baseColor,
      strokeColor,
      ringW / visualScale,
      safeOrganicState,
      radiusScreen /* effectRadiusOverride: on-screen radius */
    );

    // name rendering
    const nameScreenPx = Math.max(12, radiusScreen * 0.6);
    let nameFontPx = nameScreenPx;
    ctx.font = `bold ${nameFontPx / visualScale}px Inter, ui-sans-serif, system-ui`;
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";

    const measured = ctx.measureText(cur.name).width;
    const allowedUser = (radiusScreen * 2 * 0.9) / visualScale; // 90% of diameter
    if (measured > allowedUser) {
      const factor = allowedUser / measured;
      nameFontPx = Math.max(8, nameFontPx * factor);
      ctx.font = `bold ${nameFontPx / visualScale}px Inter, ui-sans-serif, system-ui`;
    }
    ctx.lineWidth = Math.max(1, nameFontPx * 0.12) / visualScale;
    ctx.strokeStyle = "rgba(0,0,0,0.85)";
    ctx.strokeText(cur.name, 0, 0);
    ctx.fillStyle = "#ffffff";
    ctx.fillText(cur.name, 0, 0);

    // wager label - prefer wagerDollars if available, fallback to calculating from lamports
    const usd = cur.wagerDollars ?? (solPriceUsd && cur.wagerLamports ? lamportsToSol(cur.wagerLamports) * solPriceUsd : null);
    const sol = lamportsToSol(cur.wagerLamports);
    const label = usd ? `$${usd.toFixed(2)}` : sol > 0 ? `◎${sol.toFixed(3)}` : "";
    if (label) {
      ctx.fillStyle = "#fde68a";
      const labelScreenPx = Math.max(12, radiusScreen * 0.45);
      ctx.font = `bold ${labelScreenPx / visualScale}px Inter, ui-sans-serif, system-ui`;
      ctx.textAlign = "center";
      ctx.textBaseline = "bottom";
      if (isYou) {
        ctx.shadowColor = "rgba(245,158,11,0.9)";
        ctx.shadowBlur = 8 / visualScale;
      }
      ctx.fillText(label, 0, -(radiusScreen / visualScale) - 6 / visualScale);
      if (isYou) ctx.shadowBlur = 0;
    }

    // Cash-out hold visual cue (progress arc) - own blobs only
    if (isYou) {
      const prog = (cur as any).cashoutHoldProgress as number | undefined;
      if (typeof prog === "number" && prog > 0) {
        const clamped = Math.max(0, Math.min(1, prog));
        const R = (radiusScreen / visualScale) + (ringW / visualScale) * 1.2;
        const lw = Math.max(4, ringW * 0.9) / visualScale;
        const startAng = -Math.PI / 2;
        const endAng = startAng + clamped * Math.PI * 2;

        // background track - only show when actively cashing out
        ctx.beginPath();
        ctx.arc(0, 0, R, 0, Math.PI * 2);
        ctx.strokeStyle = "rgba(148,163,184,0.25)"; // slate-400/25
        ctx.lineWidth = lw;
        ctx.stroke();

        // progress arc - use player's color
        if (clamped > 0) {
          ctx.beginPath();
          ctx.arc(0, 0, R, startAng, endAng);
          // Use the player's color for the progress arc
          const playerColor = baseColor;
          ctx.strokeStyle = playerColor;
          // Create shadow color from player color with some transparency
          const shadowColor = playerColor.replace('#', '');
          const r = parseInt(shadowColor.substr(0, 2), 16);
          const g = parseInt(shadowColor.substr(2, 2), 16);
          const b = parseInt(shadowColor.substr(4, 2), 16);
          ctx.shadowColor = `rgba(${r},${g},${b},0.8)`;
          ctx.shadowBlur = 12 / visualScale;
          ctx.lineCap = "round";
          ctx.lineWidth = lw;
          ctx.stroke();
          ctx.shadowBlur = 0;
        }
      }
    }

    // Draw mass number above player if showMass is enabled
    if (showMass) {
      ctx.fillStyle = "#ffffff";
      ctx.strokeStyle = "rgba(0,0,0,0.8)";
      const massScreenPx = Math.max(14, radiusScreen * 0.4);
      ctx.font = `bold ${massScreenPx / visualScale}px Inter, ui-sans-serif, system-ui`;
      ctx.textAlign = "center";
      ctx.textBaseline = "bottom";
      ctx.lineWidth = Math.max(1, massScreenPx * 0.12) / visualScale;
      const massText = cur.mass.toFixed(1);
      const yOffset = label ? -(radiusScreen / visualScale) - 35 / visualScale : -(radiusScreen / visualScale) - 15 / visualScale;
      ctx.strokeText(massText, 0, yOffset);
      ctx.fillText(massText, 0, yOffset);
    }

    ctx.restore();
  }
}