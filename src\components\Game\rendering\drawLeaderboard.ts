import type { TickMsg } from "@/lib/types";
import type { DisplayPlayer } from "../utils";

type LeaderboardEntryLite = {
  id: string;
  name: string;
  mass: number;
  wagerLamports?: number;
  wagerDollars?: number;
};

export function drawLeaderboard(
  ctx: CanvasRenderingContext2D,
  width: number,
  youId: string | null,
  tick: TickMsg | null,
  displayPlayers: Iterable<DisplayPlayer>,
  topN: number = 10,
  solPriceUsd?: number | null
) {
  try {
    let topEntries: LeaderboardEntryLite[] = [];
    if (tick && tick.leaderboard && Array.isArray(tick.leaderboard) && tick.leaderboard.length > 0) {
      topEntries = tick.leaderboard.slice(0, topN).map((e: any) => ({
        id: e.id,
        name: e.name,
        mass: e.mass,
        wagerLamports: e.wagerLamports,
        wagerDollars: e.wagerDollars,
      }));
    } else {
      // Fallback: approximate aggregation by player name to avoid per-cell duplication
      const agg = new Map<string, LeaderboardEntryLite>();
      for (const p of Array.from(displayPlayers)) {
        const key = p.name;
        const cur = agg.get(key) ?? { id: p.id, name: p.name, mass: 0, wagerLamports: 0, wagerDollars: 0 };
        cur.mass += p.mass;
        cur.wagerLamports = (cur.wagerLamports || 0) + (p.wagerLamports || 0);
        cur.wagerDollars = (cur.wagerDollars || 0) + (p.wagerDollars || 0);
        agg.set(key, cur);
      }
      topEntries = Array.from(agg.values())
        .sort((a, b) => b.mass - a.mass)
        .slice(0, topN);
    }

    const padding = 10;
    const lineHeight = Math.max(18, 16);
    const titleHeight = 24;
    const lbWidth = 220;
    const lbHeight = titleHeight + topEntries.length * lineHeight + padding;
    const lbX = width - lbWidth - padding;
    const lbY = padding;

    // Background
    ctx.fillStyle = "rgba(0,0,0,0.45)";
    ctx.fillRect(lbX, lbY, lbWidth, lbHeight);

    // Title
    ctx.fillStyle = "#ffffff";
    ctx.font = `bold 13px Inter, ui-sans-serif, system-ui`;
    ctx.textAlign = "left";
    ctx.textBaseline = "top";
    ctx.fillText("Leaderboard", lbX + 10, lbY + 6);

    // Items
    ctx.font = `12px Inter, ui-sans-serif, system-ui`;
    let itemY = lbY + titleHeight;
    for (let i = 0; i < topEntries.length; i++) {
      const p = topEntries[i];
      const massStr = Math.round(p.mass).toString();
      const isYouLb = youId !== null && p.id === youId;

      // Use direct dollar amount if available, otherwise compute from lamports
      const dollars = p.wagerDollars ?? 0;
      const lamports = p.wagerLamports ?? 0;
      const usdStr = dollars > 0 ? dollars.toFixed(2) : (solPriceUsd && lamports > 0 ? (lamports / 1_000_000_000 * (solPriceUsd || 0)).toFixed(2) : null);
      const rightText = usdStr ? `${massStr}   $${usdStr}` : massStr;

      if (isYouLb) {
        ctx.fillStyle = "rgba(124,58,237,0.12)";
        ctx.fillRect(lbX + 6, itemY - 2, lbWidth - 12, lineHeight);
      }
      ctx.fillStyle = isYouLb ? "#a78bfa" : "#e5e7eb";
      ctx.textAlign = "left";
      ctx.fillText(`${i + 1}. ${p.name}`, lbX + 10, itemY);
      ctx.textAlign = "right";
      ctx.fillText(rightText, lbX + lbWidth - 12, itemY);
      itemY += lineHeight;
    }
  } catch {
    // swallow any issues drawing the leaderboard to avoid breaking the render loop
  }
}