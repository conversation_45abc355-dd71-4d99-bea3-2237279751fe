import { createPortal } from "react-dom";
import { Button } from "@/components/ui/button";
import { X, XCircle } from "lucide-react";
import { GameStats } from "@/lib/types";
import CompactSizeGraph from "./CompactSizeGraph";

type LossModalProps = {
  isOpen: boolean;
  onClose: () => void;
  gameStats?: GameStats;
};

export default function LossModal({
  isOpen,
  onClose,
  gameStats,
}: LossModalProps) {
  if (!isOpen) return null;

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const modal = (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/70" onClick={onClose} />
      <div className="relative z-10 w-full max-w-lg px-4 max-h-[90vh] overflow-y-auto">
        <div className="panel-dark rounded-2xl p-6 md:p-8 shadow-xl">
          <div className="flex items-center justify-between mb-6">
            <div className="text-white/80 font-semibold">Game Over</div>
            <button
              className="text-white/60 hover:text-white"
              onClick={onClose}
              title="Close"
            >
              <X className="size-5" />
            </button>
          </div>

          {/* Loss Icon and Message */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <XCircle className="w-8 h-8 text-red-400" />
            </div>
            <h2 className="text-xl font-bold text-white mb-2">You Were Absorbed!</h2>
            <p className="text-white/60 text-sm">Better luck next time! Review your stats below.</p>
          </div>

          {/* Game Statistics */}
          {gameStats && (
            <div className="bg-neutral-900 rounded-lg p-4 mb-6 border border-neutral-700">
              <h3 className="text-lg font-semibold text-white mb-3">Game Summary</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-white/70">Time Alive:</span>
                    <span className="text-white font-medium">{formatTime(gameStats.timeAlive)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Max Mass:</span>
                    <span className="text-white font-medium">{Math.round(gameStats.maxMass)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Final Mass:</span>
                    <span className="text-white font-medium">{Math.round(gameStats.finalMass)}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-white/70">Players Killed:</span>
                    <span className="text-white font-medium">{gameStats.killCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Pellets Eaten:</span>
                    <span className="text-white font-medium">{gameStats.pelletsEaten}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Splits Used:</span>
                    <span className="text-white font-medium">{gameStats.splits}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Size Over Time Graph */}
          {gameStats && gameStats.sizeHistory && gameStats.sizeHistory.length > 0 && (
            <div className="mb-6">
              <CompactSizeGraph gameStats={gameStats} />
            </div>
          )}

          {/* Action Button */}
          <div className="flex justify-center">
            <Button
              variant="gold"
              className="px-8"
              onClick={onClose}
            >
              Return to Lobby
            </Button>
          </div>

          {/* Footer Note */}
          <div className="text-xs text-white/40 text-center mt-4">
            <p>You have been returned to the lobby. Join a new game to try again!</p>
          </div>
        </div>
      </div>
    </div>
  );

  return typeof document !== "undefined" ? createPortal(modal, document.body) : modal;
}