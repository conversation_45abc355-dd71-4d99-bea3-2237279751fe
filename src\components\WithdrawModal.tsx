import { useMemo, useState } from "react";
import { createPortal } from "react-dom";
import { Button } from "@/components/ui/button";
import { X, Send } from "lucide-react";
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";

type Props = {
  open: boolean;
  onClose: () => void;
  pubkey: string | null;
  balanceLamports: number | null;
  solPriceUsd: number | null;
  onRefresh: () => void | Promise<void>;
};

function clamp(n: number, min: number, max: number) {
  return Math.min(max, Math.max(min, n));
}

export default function WithdrawModal({
  open,
  onClose,
  pubkey,
  balanceLamports,
  solPriceUsd,
  onRefresh,
}: Props) {
  // Convex mutations for transaction tracking
  const createTransaction = useMutation((api as any).transactions.createTransaction);
  const updateTransactionStatus = useMutation((api as any).transactions.updateTransactionStatus);
  const updateTransactionSignature = useMutation((api as any).transactions.updateTransactionSignature);

  const [toAddr, setToAddr] = useState("");
  const [amountInput, setAmountInput] = useState("0");
  const [submitting, setSubmitting] = useState(false);
  const [notice, setNotice] = useState<{ type: "success" | "error" | "info"; message: string } | null>(null);
  const [signature, setSignature] = useState<string | null>(null);

  const handleClose = () => {
    // clear local UI state then close
    setNotice(null);
    setSignature(null);
    onClose();
  };

  const balanceSol = (balanceLamports ?? 0) / 1_000_000_000;
  const parsedAmountSol = useMemo(() => {
    const n = parseFloat(amountInput);
    return Number.isFinite(n) ? clamp(n, 0, balanceSol) : 0;
  }, [amountInput, balanceSol]);

  const percent = useMemo(() => {
    if (balanceSol <= 0) return 0;
    return clamp(Math.round((parsedAmountSol / balanceSol) * 100), 0, 100);
  }, [parsedAmountSol, balanceSol]);

  const amountUsd = useMemo(() => {
    if (!solPriceUsd) return 0;
    return parsedAmountSol * solPriceUsd;
  }, [parsedAmountSol, solPriceUsd]);

  const setPercent = (p: number) => {
    const next = clamp(p, 0, 100);
    const amt = balanceSol * (next / 100);
    setAmountInput(amt.toFixed(4));
  };

  const handleConfirm = async () => {
    if (!pubkey) {
      setNotice({ type: "error", message: "Connect wallet first" });
      return;
    }
    const to = toAddr.trim();
    if (!to || to.length < 30) {
      setNotice({ type: "error", message: "Enter a valid recipient address" });
      return;
    }
    if (parsedAmountSol <= 0) {
      setNotice({ type: "error", message: "Enter an amount greater than 0" });
      return;
    }
    if ((balanceLamports ?? 0) <= 0) {
      setNotice({ type: "error", message: "Insufficient balance" });
      return;
    }

    let transactionId: string | null = null;

    try {
      setSubmitting(true);
      setNotice(null);
      const { solToLamports, sendLamports } = await import("@/lib/web3auth");
      const lamports = solToLamports(parsedAmountSol);
      if (lamports <= 0) throw new Error("Invalid amount");
      if ((balanceLamports ?? 0) < lamports) throw new Error("Insufficient balance");

      // Create transaction record before sending
      try {
        transactionId = await createTransaction({
          publicAddress: pubkey,
          type: "withdraw",
          amountLamports: lamports,
          amountSol: parsedAmountSol,
          amountUsd: solPriceUsd ? parsedAmountSol * solPriceUsd : undefined,
          blockchainNetwork: "devnet", // You might want to make this dynamic
          metadata: {
            recipientAddress: to,
            solPriceUsd: solPriceUsd || undefined,
          },
        });
      } catch (e) {
        console.error("Failed to create transaction record:", e);
      }

      const sig = await sendLamports(to, lamports);

      // Update transaction with signature
      if (transactionId) {
        try {
          await updateTransactionSignature({
            transactionId: transactionId as any,
            signature: sig,
          });
          // Mark as confirmed since we have the signature
          await updateTransactionStatus({
            transactionId: transactionId as any,
            status: "confirmed",
          });
        } catch (e) {
          console.error("Failed to update transaction:", e);
        }
      }

      // show concise success message and keep the signature in its own field so it can be opened in Solscan
      setNotice({ type: "success", message: "Withdraw sent." });
      setSignature(sig);
      setToAddr("");
      setAmountInput("0");
      try {
        await onRefresh();
      } catch {}
      // keep the modal open so the user can copy/click the signature
    } catch (e: any) {
      // Update transaction status to failed if we have a transaction ID
      if (transactionId) {
        try {
          await updateTransactionStatus({
            transactionId: transactionId as any,
            status: "failed",
          });
        } catch (updateError) {
          console.error("Failed to update transaction status to failed:", updateError);
        }
      }
      setNotice({ type: "error", message: "Withdraw failed: " + (e?.message || String(e)) });
    } finally {
      setSubmitting(false);
    }
  };

  if (!open) return null;

  const modal = (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/70" onClick={() => !submitting && handleClose()} />
      <div className="relative z-10 w-full max-w-md px-4">
        <div className="panel-dark rounded-2xl p-6 md:p-8 shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="text-white/80 font-semibold">Withdraw</div>
            <button
              className="text-white/60 hover:text-white"
              onClick={() => !submitting && handleClose()}
              title="Close"
            >
              <X className="size-5" />
            </button>
          </div>
          <div className="mb-4 text-xs text-white/60">
            Balance: {balanceSol.toFixed(4)} SOL
            {solPriceUsd ? ` (~$${(balanceSol * solPriceUsd).toFixed(2)})` : ""}
          </div>

          <div className="space-y-4">
            {notice && (
              <div
                className={`p-2 rounded-md text-sm ${
                  notice.type === "success"
                    ? "bg-green-700 text-white"
                    : notice.type === "error"
                    ? "bg-red-700 text-white"
                    : "bg-blue-700 text-white"
                }`}
              >
                <div>{notice.message}</div>
                {signature && (
                  <div className="mt-1 text-xs font-mono text-white/90 bg-black/10 rounded px-2 py-1">
                    <a
                      href={`https://solscan.io/tx/${signature}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      title={signature}
                      className="block overflow-x-auto break-all underline"
                    >
                      Signature: {signature}
                    </a>
                  </div>
                )}
              </div>
            )}
            <div>
              <label className="block text-sm text-white/70 mb-2">Recipient Wallet</label>
              <input
                className="w-full rounded-md border border-neutral-700 bg-neutral-900 px-3 py-2 text-sm outline-none focus:ring-2 focus:ring-amber-500/50"
                placeholder="Enter Solana address"
                value={toAddr}
                onChange={(e) => setToAddr(e.target.value)}
                disabled={submitting}
              />
            </div>

            <div>
              <label className="block text-sm text-white/70 mb-2">Amount</label>
              <div className="flex items-center gap-2">
                <input
                  className="w-full rounded-md border border-neutral-700 bg-neutral-900 px-3 py-2 text-sm outline-none focus:ring-2 focus:ring-amber-500/50"
                  value={amountInput}
                  onChange={(e) => setAmountInput(e.target.value)}
                  inputMode="decimal"
                  disabled={submitting}
                />
                <div className="text-xs text-white/60 px-2 py-1 rounded-md border border-neutral-700 bg-neutral-900 select-none">
                  SOL
                </div>
              </div>
              <div className="mt-1 text-xs text-white/50">≈ ${amountUsd.toFixed(2)} USD</div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs text-white/60">
                <span>0%</span>
                <span>{percent}%</span>
                <span>100%</span>
              </div>
              <input
                type="range"
                min={0}
                max={100}
                step={5}
                value={percent}
                onChange={(e) => setPercent(parseInt(e.target.value || "0", 10))}
                disabled={submitting || balanceSol <= 0}
                className="w-full accent-amber-500"
              />
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  className={`flex-1 border-neutral-700 ${percent === 0 ? "bg-neutral-800" : ""}`}
                  onClick={() => setPercent(0)}
                  disabled={submitting}
                >
                  0%
                </Button>
                <Button
                  variant="outline"
                  className={`flex-1 border-neutral-700 ${percent === 50 ? "bg-neutral-800" : ""}`}
                  onClick={() => setPercent(50)}
                  disabled={submitting}
                >
                  50%
                </Button>
                <Button
                  variant="outline"
                  className={`flex-1 border-neutral-700 ${percent === 100 ? "bg-neutral-800" : ""}`}
                  onClick={() => setPercent(100)}
                  disabled={submitting}
                >
                  100%
                </Button>
              </div>
              <div className="text-[11px] text-white/40">
                You will send {parsedAmountSol.toFixed(4)} SOL
                {solPriceUsd ? ` (~$${amountUsd.toFixed(2)})` : ""}
              </div>
            </div>

            <div className="mt-4 flex items-center justify-end gap-2">
              <Button variant="outline" onClick={() => handleClose()} disabled={submitting}>
                Cancel
              </Button>
              <Button
                variant="gold"
                className="gap-2"
                onClick={handleConfirm}
                disabled={
                  submitting ||
                  !pubkey ||
                  !toAddr.trim() ||
                  parsedAmountSol <= 0 ||
                  parsedAmountSol > balanceSol
                }
              >
                <Send className="size-4" />
                {submitting ? "Sending…" : "Confirm"}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return typeof document !== "undefined" ? createPortal(modal, document.body) : modal;
}