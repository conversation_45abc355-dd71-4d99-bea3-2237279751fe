import { createPortal } from "react-dom";
import { But<PERSON> } from "@/components/ui/button";
import { X, Trophy, Clock, Target, TrendingUp } from "lucide-react";
import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import SizeOverTimeGraph from "./SizeOverTimeGraph";

type GameHistoryModalProps = {
  isOpen: boolean;
  onClose: () => void;
  publicAddress?: string;
};

export default function GameHistoryModal({
  isOpen,
  onClose,
  publicAddress,
}: GameHistoryModalProps) {
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);
  
  const gameSessions = useQuery(
    (api as any).gameSessions?.getUserGameSessions,
    publicAddress ? { publicAddress, limit: 20 } : "skip"
  );

  const userStats = useQuery(
    (api as any).gameSessions?.getUserStatsSummary,
    publicAddress ? { publicAddress } : "skip"
  );

  if (!isOpen) return null;

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getResultColor = (result: string) => {
    switch (result) {
      case 'cashout':
        return 'text-green-400';
      case 'consumed':
        return 'text-red-400';
      case 'disconnect':
        return 'text-yellow-400';
      default:
        return 'text-white/60';
    }
  };

  const getResultIcon = (result: string) => {
    switch (result) {
      case 'cashout':
        return <Trophy className="w-4 h-4" />;
      case 'consumed':
        return <Target className="w-4 h-4" />;
      case 'disconnect':
        return <X className="w-4 h-4" />;
      default:
        return null;
    }
  };

  const modal = (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/70" onClick={onClose} />
      <div className="relative z-10 w-full max-w-4xl px-4 max-h-[90vh] overflow-hidden">
        <div className="panel-dark rounded-2xl p-6 md:p-8 shadow-xl">
          <div className="flex items-center justify-between mb-6">
            <div className="text-white/80 font-semibold text-xl">Game History</div>
            <button
              className="text-white/60 hover:text-white"
              onClick={onClose}
              title="Close"
            >
              <X className="size-5" />
            </button>
          </div>

          {/* User Statistics Summary */}
          {userStats && (
            <div className="bg-neutral-900 rounded-lg p-4 mb-6 border border-neutral-700">
              <h3 className="text-lg font-semibold text-white mb-3">Overall Statistics</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{userStats.totalGames}</div>
                  <div className="text-white/60">Total Games</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{userStats.totalCashouts}</div>
                  <div className="text-white/60">Cashouts</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{Math.round(userStats.maxMassEver)}</div>
                  <div className="text-white/60">Max Mass</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-400">{userStats.totalKills}</div>
                  <div className="text-white/60">Total Kills</div>
                </div>
              </div>
              <div className="mt-4 pt-4 border-t border-neutral-700 grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="text-white/70">Win Rate:</span>
                  <span className="text-white font-medium">{userStats.winRate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/70">Avg. Time Alive:</span>
                  <span className="text-white font-medium">{formatTime(userStats.averageTimeAlive)}</span>
                </div>
              </div>
            </div>
          )}

          {/* Game Sessions List */}
          <div className="max-h-96 overflow-y-auto">
            {gameSessions && gameSessions.length > 0 ? (
              <div className="space-y-3">
                {gameSessions.map((session: any) => (
                  <div
                    key={session._id}
                    className="bg-neutral-900 rounded-lg p-4 border border-neutral-700 hover:border-neutral-600 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <div className={`flex items-center gap-1 ${getResultColor(session.stats.gameResult)}`}>
                          {getResultIcon(session.stats.gameResult)}
                          <span className="font-medium capitalize">{session.stats.gameResult}</span>
                        </div>
                        <span className="text-white/40">•</span>
                        <span className="text-white/60 text-sm">{formatDate(session.createdAt)}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-1 text-white/60">
                          <Clock className="w-4 h-4" />
                          <span className="text-sm">{formatTime(session.stats.timeAlive)}</span>
                        </div>
                        {session.stats.sizeHistory && session.stats.sizeHistory.length > 0 && (
                          <button
                            onClick={() => setSelectedSessionId(
                              selectedSessionId === session._id ? null : session._id
                            )}
                            className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-colors ${
                              selectedSessionId === session._id
                                ? 'bg-blue-600 text-white'
                                : 'bg-neutral-700 hover:bg-neutral-600 text-white/80'
                            }`}
                            title="View size graph"
                          >
                            <TrendingUp className="w-3 h-3" />
                            Graph
                          </button>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex justify-between">
                        <span className="text-white/70">Max Mass:</span>
                        <span className="text-white font-medium">{Math.round(session.stats.maxMass)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-white/70">Final Mass:</span>
                        <span className="text-white font-medium">{Math.round(session.stats.finalMass)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-white/70">Kills:</span>
                        <span className="text-white font-medium">{session.stats.killCount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-white/70">Pellets:</span>
                        <span className="text-white font-medium">{session.stats.pelletsEaten}</span>
                      </div>
                    </div>

                    {session.stats.gameResult === 'consumed' && session.stats.consumedBy && (
                      <div className="mt-2 text-sm text-red-400">
                        Consumed by player {session.stats.consumedBy.slice(0, 8)}...
                      </div>
                    )}

                    {/* Size over time graph */}
                    {selectedSessionId === session._id && session.stats.sizeHistory && (
                      <div className="mt-4 pt-4 border-t border-neutral-700">
                        <SizeOverTimeGraph gameStats={session.stats} />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-white/40 mb-2">No games played yet</div>
                <div className="text-white/60 text-sm">Start playing to see your game history here!</div>
              </div>
            )}
          </div>

          {/* Close Button */}
          <div className="mt-6 flex justify-center">
            <Button
              variant="gold"
              onClick={onClose}
              className="px-8"
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  return typeof document !== "undefined" ? createPortal(modal, document.body) : modal;
}