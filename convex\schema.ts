import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    publicAddress: v.string(),
    firstLoginAt: v.number(),
    lastLoginAt: v.number(),
    loginCount: v.number(),
    // Optional fields for additional user data
    displayName: v.optional(v.string()),
    email: v.optional(v.string()),
  })
    .index("by_public_address", ["publicAddress"]),
  
  transactions: defineTable({
    // User identification
    publicAddress: v.string(),
    userId: v.optional(v.id("users")), // Reference to users table
    
    // Transaction details
    type: v.union(
      v.literal("withdraw"),
      v.literal("join_lobby"),
      v.literal("cashout")
    ),
    status: v.union(
      v.literal("pending"),
      v.literal("confirmed"),
      v.literal("failed")
    ),
    
    // Amounts (in lamports for precision)
    amountLamports: v.number(),
    amountSol: v.number(), // For easier querying
    amountUsd: v.optional(v.number()), // USD value at time of transaction
    
    // Blockchain data
    signature: v.optional(v.string()), // Transaction signature
    blockchainNetwork: v.optional(v.string()), // e.g., "devnet", "mainnet"
    
    // Game-specific data
    gameId: v.optional(v.string()), // For join_lobby and cashout transactions
    joinCode: v.optional(v.string()), // For join_lobby transactions
    
    // Additional metadata
    metadata: v.optional(v.object({
      recipientAddress: v.optional(v.string()), // For withdraw transactions
      platformFee: v.optional(v.number()), // For cashout transactions (in lamports)
      solPriceUsd: v.optional(v.number()), // SOL price at time of transaction
    })),
    
    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_public_address", ["publicAddress"])
    .index("by_user_id", ["userId"])
    .index("by_type", ["type"])
    .index("by_status", ["status"])
    .index("by_signature", ["signature"])
    .index("by_created_at", ["createdAt"]),

  // Game sessions and statistics
  gameSessions: defineTable({
    // Player identification
    publicAddress: v.string(),
    userId: v.optional(v.id("users")), // Reference to users table
    playerName: v.string(),
    
    // Game identification
    gameId: v.string(), // Unique game instance ID
    joinCode: v.optional(v.string()), // Join code used to enter the game
    
    // Game statistics
    stats: v.object({
      timeAlive: v.number(), // in milliseconds
      maxMass: v.number(),
      killCount: v.number(),
      pelletsEaten: v.number(),
      ejectedMasses: v.number(),
      splits: v.number(),
      startTime: v.number(),
      endTime: v.optional(v.number()),
      finalMass: v.number(),
      wagerStart: v.number(), // starting wager in lamports
      wagerEnd: v.number(), // ending wager in lamports
      gameResult: v.union(
        v.literal("cashout"),
        v.literal("consumed"),
        v.literal("disconnect")
      ),
      consumedBy: v.optional(v.string()), // player ID who consumed this player
      playerColor: v.optional(v.string()), // player's color (hex)
      // Size history for graphs - array of {timestamp, mass} points
      sizeHistory: v.optional(v.array(v.object({
        timestamp: v.number(), // milliseconds since game start
        mass: v.number(), // total mass at this point
      }))),
    }),
    
    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_public_address", ["publicAddress"])
    .index("by_user_id", ["userId"])
    .index("by_game_id", ["gameId"])
    .index("by_created_at", ["createdAt"])
    .index("by_game_result", ["stats.gameResult"]),
});