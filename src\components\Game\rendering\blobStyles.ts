export type BlobStyle = {
  base: string;
  dark: string;
};

// Stable palette for pellets
export const PELLET_COLORS = [
  "#ef4444", "#fb923c", "#facc15", "#a3e635", "#22c55e",
  "#2dd4bf", "#3b82f6", "#60a5fa", "#818cf8", "#c084fc", "#f472b6"
];

export function darkenColor(hex: string, percent: number) {
  const h = hex.replace("#", "");
  const r = Math.max(0, Math.min(255, Math.floor(parseInt(h.substring(0, 2), 16) * (100 - percent) / 100)));
  const g = Math.max(0, Math.min(255, Math.floor(parseInt(h.substring(2, 4), 16) * (100 - percent) / 100)));
  const b = Math.max(0, Math.min(255, Math.floor(parseInt(h.substring(4, 6), 16) * (100 - percent) / 100)));
  const rs = r.toString(16).padStart(2, "0");
  const gs = g.toString(16).padStart(2, "0");
  const bs = b.toString(16).padStart(2, "0");
  return `#${rs}${gs}${bs}`;
}

export function randPhase() {
  return Math.random() * Math.PI * 2;
}

export function ensureStyle(
  map: Map<string, BlobStyle>,
  id: string,
  base: string
) {
  if (!map.has(id)) {
    map.set(id, {
      base,
      dark: darkenColor(base, 30),
    });
  }
  // non-null because we set it above
  return map.get(id)!;
}

// Deterministic hash helper to map an id to an index/variation.
export function hashStringToInt(s: string) {
  let h = 0;
  for (let i = 0; i < s.length; i++) {
    h = (h << 5) - h + s.charCodeAt(i);
    h |= 0;
  }
  return Math.abs(h);
}

// Pick a stable color for a pellet id from PELLET_COLORS.
export function colorForPellet(id: string) {
  const idx = hashStringToInt(id) % PELLET_COLORS.length;
  return PELLET_COLORS[idx];
}

// Small deterministic size multiplier per-pellet to add variety.
export function pelletSizeMultiplier(id: string) {
  const hv = hashStringToInt(id) % 100; // 0..99
  // map to ~0.9 .. 1.3
  return 0.9 + hv / 250;
}