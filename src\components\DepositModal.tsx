import { createPortal } from "react-dom";
import { Button } from "@/components/ui/button";
import { X, Copy } from "lucide-react";

type Props = {
  open: boolean;
  onClose: () => void;
  pubkey: string | null;
};

export default function DepositModal({ open, onClose, pubkey }: Props) {
  const handleCopy = () => {
    if (!pubkey) return;
    navigator.clipboard?.writeText(pubkey).catch(() => {});
  };

  const handleClose = () => {
    onClose();
  };

  if (!open) return null;

  const qrSrc =
    pubkey && pubkey.length > 0
      ? `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(
          pubkey
        )}`
      : "";

  const modal = (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/70" onClick={handleClose} />
      <div className="relative z-10 w-full max-w-md px-4">
        <div className="panel-dark rounded-2xl p-6 md:p-8 shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="text-white/80 font-semibold">Deposit</div>
            <button
              className="text-white/60 hover:text-white"
              onClick={handleClose}
              title="Close"
            >
              <X className="size-5" />
            </button>
          </div>

          <div className="flex flex-col items-center gap-4">
            {qrSrc ? (
              <img
                src={qrSrc}
                alt="Wallet QR Code"
                className="w-48 h-48 rounded-md bg-white/5 p-2"
              />
            ) : (
              <div className="w-48 h-48 rounded-md bg-neutral-900 flex items-center justify-center text-xs text-white/60">
                No address
              </div>
            )}

            <div className="text-sm text-white/60 break-all font-mono text-center px-2">
              {pubkey ?? "Not connected"}
            </div>

            <div className="flex gap-2 w-full">
              <Button className="flex-1" variant="outline" onClick={handleCopy} disabled={!pubkey}>
                <Copy className="size-4" />
                Copy Address
              </Button>
              <Button
                className="flex-1"
                onClick={() => {
                  if (pubkey) window.open(`https://solscan.io/account/${pubkey}`, "_blank");
                }}
                disabled={!pubkey}
              >
                View on Solscan
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return typeof document !== "undefined" ? createPortal(modal, document.body) : modal;
}