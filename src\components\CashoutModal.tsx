import { createPortal } from "react-dom";
import { But<PERSON> } from "@/components/ui/button";
import { X, Copy, ExternalLink, CheckCircle } from "lucide-react";
import { useState } from "react";
import { GameStats } from "@/lib/types";
import CompactSizeGraph from "./CompactSizeGraph";

type CashoutModalProps = {
  isOpen: boolean;
  onClose: () => void;
  amountLamports: number;
  signature: string;
  solPriceUsd?: number | null;
  gameStats?: GameStats;
};

export default function CashoutModal({
  isOpen,
  onClose,
  amountLamports,
  signature,
  solPriceUsd,
  gameStats,
}: CashoutModalProps) {
  const [copied, setCopied] = useState(false);

  if (!isOpen) return null;

  const amountSol = amountLamports / 1_000_000_000;
  const amountUsd = solPriceUsd ? amountSol * solPriceUsd : null;

  const handleViewTransaction = () => {
    // Open transaction in Solana Explorer
    const explorerUrl = `https://solscan.io/tx/${signature}?cluster=devnet`;
    window.open(explorerUrl, '_blank');
  };

  const handleCopySignature = async () => {
    try {
      await navigator.clipboard.writeText(signature);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy signature:', err);
    }
  };

  const handleClose = () => {
    setCopied(false);
    onClose();
  };

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const modal = (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/70" onClick={handleClose} />
      <div className="relative z-10 w-full max-w-lg px-4 max-h-[90vh] overflow-y-auto">
        <div className="panel-dark rounded-2xl p-6 md:p-8 shadow-xl">
          <div className="flex items-center justify-between mb-6">
            <div className="text-white/80 font-semibold">Cashout Successful</div>
            <button
              className="text-white/60 hover:text-white"
              onClick={handleClose}
              title="Close"
            >
              <X className="size-5" />
            </button>
          </div>

          {/* Success Icon and Message */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-green-400" />
            </div>
            <h2 className="text-xl font-bold text-white mb-2">Cashout Complete!</h2>
            <p className="text-white/60 text-sm">Your winnings have been sent to your wallet</p>
          </div>

          {/* Amount Display */}
          <div className="bg-neutral-900 rounded-lg p-4 mb-6 border border-neutral-700">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400 mb-1">
                {amountSol.toFixed(4)} SOL
              </div>
              {amountUsd && (
                <div className="text-white/60 text-sm">
                  ≈ ${amountUsd.toFixed(2)} USD
                </div>
              )}
            </div>
          </div>

          {/* Game Statistics */}
          {gameStats && (
            <div className="bg-neutral-900 rounded-lg p-4 mb-6 border border-neutral-700">
              <h3 className="text-lg font-semibold text-white mb-3">Game Summary</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-white/70">Time Alive:</span>
                    <span className="text-white font-medium">{formatTime(gameStats.timeAlive)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Max Mass:</span>
                    <span className="text-white font-medium">{Math.round(gameStats.maxMass)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Final Mass:</span>
                    <span className="text-white font-medium">{Math.round(gameStats.finalMass)}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-white/70">Players Killed:</span>
                    <span className="text-white font-medium">{gameStats.killCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Pellets Eaten:</span>
                    <span className="text-white font-medium">{gameStats.pelletsEaten}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Splits Used:</span>
                    <span className="text-white font-medium">{gameStats.splits}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Size Over Time Graph */}
          {gameStats && gameStats.sizeHistory && gameStats.sizeHistory.length > 0 && (
            <div className="mb-6">
              <CompactSizeGraph gameStats={gameStats} />
            </div>
          )}

          {/* Transaction Signature */}
          <div className="mb-6">
            <label className="block text-sm text-white/70 mb-2">Transaction Signature</label>
            <div className="bg-neutral-900 rounded-md border border-neutral-700 p-3 flex items-center justify-between">
              <code className="text-xs text-white/80 font-mono truncate flex-1 mr-2">
                {signature}
              </code>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopySignature}
                className="text-xs border-neutral-600 hover:border-neutral-500"
              >
                <Copy className="size-3 mr-1" />
                {copied ? "Copied!" : "Copy"}
              </Button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              className="flex-1 border-neutral-700 hover:border-neutral-600"
              onClick={handleViewTransaction}
            >
              <ExternalLink className="size-4 mr-2" />
              View on Explorer
            </Button>
            <Button
              variant="gold"
              className="flex-1"
              onClick={handleClose}
            >
              Return to Lobby
            </Button>
          </div>

          {/* Footer Note */}
          <div className="text-xs text-white/40 text-center mt-4 space-y-1">
            <p>A 5% platform fee has been deducted from your winnings.</p>
            <p>You have been returned to the lobby. Join a new game to continue playing.</p>
          </div>
        </div>
      </div>
    </div>
  );

  return typeof document !== "undefined" ? createPortal(modal, document.body) : modal;
}