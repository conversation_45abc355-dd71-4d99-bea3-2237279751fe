import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Copy, RefreshCw, Wallet as WalletIcon, History } from "lucide-react";
import WithdrawModal from "./WithdrawModal";
import DepositModal from "./DepositModal";
import ActivityHistoryModal from "./ActivityHistoryModal";
 
type Props = {
  pubkey: string | null;
  balanceLamports: number | null;
  balanceLoading: boolean;
  solPriceUsd: number | null;
  onRefresh: () => void | Promise<void>;
  onWithdraw: () => void;
};
 
function formatUsd(balanceLamports: number | null, solPriceUsd: number | null): string {
  if (balanceLamports == null || solPriceUsd == null) return "$0.00";
  return `$${(((balanceLamports ?? 0) / 1_000_000_000) * (solPriceUsd ?? 0)).toFixed(2)}`;
}
 
function formatSol(balanceLamports: number | null): string {
  return `${(((balanceLamports ?? 0) / 1_000_000_000)).toFixed(4)} SOL`;
}
 
export default function WalletPanel({
  pubkey,
  balanceLamports,
  balanceLoading,
  solPriceUsd,
  onRefresh,
  onWithdraw: _onWithdraw,
}: Props) {
  const copy = (text: string) => {
    navigator.clipboard?.writeText(text).catch(() => {});
  };
 
  const [open, setOpen] = useState(false);
  const [depositOpen, setDepositOpen] = useState(false);
  const [historyOpen, setHistoryOpen] = useState(false);
 
  return (
    <div className="panel-dark w-full max-w-md p-6 md:p-8 rounded-2xl">
      <div className="flex items-center justify-between text-sm text-white/70">
        <div className="flex items-center gap-2">
          <WalletIcon className="size-4 text-emerald-400" />
          <span>Wallet</span>
        </div>
        <div className="flex items-center gap-4">
          <button
            className="inline-flex items-center gap-1 text-white/70 hover:text-white"
            onClick={() => pubkey && copy(pubkey)}
            title="Copy Address"
          >
            <Copy className="size-3.5" /> Copy Address
          </button>
          <button
            className="inline-flex items-center gap-1 text-white/70 hover:text-white"
            onClick={onRefresh}
            title="Refresh"
          >
            <RefreshCw className="size-3.5" /> Refresh Balance
          </button>
        </div>
      </div>
 
      <div className="mt-4 text-center">
        <div className="text-4xl font-extrabold text-white">
          {balanceLoading ? "…" : formatUsd(balanceLamports, solPriceUsd)}
        </div>
        <div className="text-xs text-white/50">
          {balanceLoading ? "Loading…" : formatSol(balanceLamports)}
        </div>
      </div>
 
      <div className="mt-6 space-y-3">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <Button
            className="w-full"
            variant="outline"
            onClick={() => setDepositOpen(true)}
            disabled={!pubkey}
          >
            Deposit
          </Button>
          <Button
            className="w-full"
            variant="outline"
            onClick={() => setOpen(true)}
            disabled={!pubkey || !balanceLamports || balanceLamports <= 0}
          >
            Withdraw
          </Button>
        </div>
        <Button
          className="w-full"
          variant="outline"
          onClick={() => setHistoryOpen(true)}
          disabled={!pubkey}
        >
          <History className="w-4 h-4 mr-2" />
          Activity History
        </Button>
      </div>
 
      <WithdrawModal
        open={open}
        onClose={() => setOpen(false)}
        pubkey={pubkey}
        balanceLamports={balanceLamports}
        solPriceUsd={solPriceUsd}
        onRefresh={onRefresh}
      />
 
      <DepositModal
        open={depositOpen}
        onClose={() => setDepositOpen(false)}
        pubkey={pubkey}
      />

      <ActivityHistoryModal
        isOpen={historyOpen}
        onClose={() => setHistoryOpen(false)}
        publicAddress={pubkey || undefined}
      />
    </div>
  );
}