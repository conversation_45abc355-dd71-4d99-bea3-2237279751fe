/**
 * Draw a simple circular blob consisting of:
 * 1) A filled circle (main body)
 * 2) An optional stroke outline
 */
export function drawSimpleCircle(
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  r: number,
  fillColor: string,
  strokeColor?: string,
  strokeWidth?: number
) {
  // Always draw a simple circle
  ctx.beginPath();
  ctx.arc(x, y, r, 0, Math.PI * 2);
  
  // Fill the circle
  ctx.fillStyle = fillColor;
  ctx.fill();
  
  // Add stroke if specified
  if (strokeColor && strokeWidth && strokeWidth > 0) {
    ctx.strokeStyle = strokeColor;
    ctx.lineWidth = strokeWidth;
    ctx.stroke();
  }
}