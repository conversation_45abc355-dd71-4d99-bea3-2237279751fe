# Agar.io-style Wager MVP (Solana + Web3Auth)

Authoritative multiplayer game skeleton with a centralized Solana pot wallet. Players log in with Web3Auth, deposit a wager (SOL) to join, play in a simple 2D world, and request cashout. Built with:
- Frontend: Vite + React + TypeScript
- Backend: Express + Socket.IO (authoritative tick loop)
- Solana: @solana/web3.js on devnet
- Auth/Wallet UX: Web3Auth (modal + OpenLogin)

DISCLAIMER: This is an MVP for demonstration. Do not use as-is in production. Security, cheating prevention, and on-chain accounting require more hardening and audits.

--------------------------------------------------------------------------------

## Quick Start

Prereqs:
- Node 22+ (package.json engines)
- Corepack enabled (for pnpm): `corepack enable`
- pnpm installed (Core<PERSON> will download pinned version automatically)

Install deps:
- `pnpm install`

Create environment file:
- Copy [.env.example](.env.example) to `.env`
- Fill in `VITE_WEB3AUTH_CLIENT_ID` (see below)
- Optionally set `POT_PRIVATE_KEY` to a real devnet key (JSON array)

Run dev (frontend + backend together):
- `pnpm run dev:all`
  - Frontend: http://localhost:5173
  - Backend: http://localhost:8787 (REST: /api/*, WS: /ws)

If you don’t set `POT_PRIVATE_KEY`, the server generates a volatile keypair on boot. Fund it on devnet before testing wagers.

--------------------------------------------------------------------------------

## Environment Variables

See [.env.example](.env.example).

Server (Node/Express):
- PORT: default 8787
- SOLANA_NETWORK: devnet
- POT_PRIVATE_KEY: JSON array of secret key (Uint8Array) for pot wallet
  - Example format (DO NOT USE IN PROD): `[12,34,56,...]`

Frontend (Vite):
- VITE_SOLANA_NETWORK: devnet
- VITE_WEB3AUTH_CLIENT_ID: Obtain from https://dashboard.web3auth.io

Tip: Ensure `.env` is ignored by git (check .gitignore).

--------------------------------------------------------------------------------

## Getting a Web3Auth Client ID

1) Go to https://dashboard.web3auth.io
2) Create a project and copy the Client ID
3) Paste it into your `.env` as `VITE_WEB3AUTH_CLIENT_ID=...`
4) Restart the dev server if already running

Note: The package @web3auth/modal v7 shows peer warnings with React 19. It still works for MVP, but you should plan to upgrade Web3Auth packages to versions compatible with React 19 in the future.

--------------------------------------------------------------------------------

## Devnet Funding for Pot Wallet

When the server starts, it prints pot address and balance. If you didn’t set `POT_PRIVATE_KEY`, a volatile keypair is used and needs funding:

- Use the Solana faucet:
  - https://faucet.solana.com (select Devnet)
- Or with Solana CLI:
  - `solana airdrop 2 <POT_PUBLIC_KEY> --url https://api.devnet.solana.com`

To generate a pot keypair as JSON array (Node snippet):
```js
import { Keypair } from "@solana/web3.js";
const k = Keypair.generate();
console.log(JSON.stringify(Array.from(k.secretKey)));
console.log("pub:", k.publicKey.toBase58());
```

Paste the JSON array into `POT_PRIVATE_KEY` in `.env`.

--------------------------------------------------------------------------------

## Wager Flow (Centralized Pot MVP)

1) Player logs in with Web3Auth (non-custodial wallet).
2) Frontend calls POST `/api/wager/initiate` with:
   - `amountLamports`
   - `playerPubkey`
   - Server returns `{ joinCode, potAddress, expiresAt }`
3) Frontend sends a SystemProgram transfer (wallet → potAddress) for the amount.
4) Frontend calls POST `/api/wager/confirm` with:
   - `{ joinCode, signature }`
   - Server verifies the confirmed transaction, marks the joinCode as authorized
5) Frontend opens Socket.IO and sends `"join"` with `{ joinCode, tempName }`
6) Server allows entry and authoritative loop updates positions, broadcasts ticks.

Cashout:
- POST `/api/cashout` with `{ playerPubkey, amountLamports }`
- Server sends SOL from pot to the player.

IMPORTANT: Accounting here is minimal. Replace with authoritative per-player balances and strict checks before enabling real wagers.

--------------------------------------------------------------------------------

## REST API (Dev)

- GET `/api/health` → `{ ok, network, potAddress }`
- GET `/api/pot-address` → `{ potAddress }`

- POST `/api/wager/initiate`
  - Body: `{ amountLamports: number, playerPubkey: string }`
  - Resp: `{ joinCode, potAddress, amountLamports, memo?, expiresAt }`

- POST `/api/wager/confirm`
  - Body: `{ joinCode: string, signature: string }`
  - Resp: `{ status: "confirmed", joinCode }`

- POST `/api/cashout`
  - Body: `{ playerPubkey: string, amountLamports: number }`
  - Resp: `{ signature }`

Example cURL:
```bash
curl -s http://localhost:8787/api/health

curl -s http://localhost:8787/api/pot-address

curl -s -X POST http://localhost:8787/api/wager/initiate \
  -H "Content-Type: application/json" \
  -d '{"amountLamports":********,"playerPubkey":"<PLAYER_PUBLIC_KEY>"}'

curl -s -X POST http://localhost:8787/api/wager/confirm \
  -H "Content-Type: application/json" \
  -d '{"joinCode":"<JOIN_CODE>","signature":"<SOL_TX_SIGNATURE>"}'

curl -s -X POST http://localhost:8787/api/cashout \
  -H "Content-Type: application/json" \
  -d '{"playerPubkey":"<PLAYER_PUBLIC_KEY>","amountLamports":********}'
```

--------------------------------------------------------------------------------

## Frontend Usage

- Login button opens Web3Auth modal. You must provide `VITE_WEB3AUTH_CLIENT_ID`.
- Enter a temp name and wager SOL amount.
- Click “Deposit + Join” to run the flow (initiate → transfer → confirm → join).
- Move your cell by moving the mouse (client sends target positions; server moves your cell).
- Cashout section sends SOL from pot to your wallet.

The canvas shows a simple world with your cell and others. Speed is inversely related to mass.

--------------------------------------------------------------------------------

## Files of Interest

- Backend server: server/index.ts
- Frontend entry: src/main.tsx
- Frontend app: src/App.tsx
- Web3Auth glue: src/lib/web3auth.ts
- REST client: src/lib/api.ts
- Vite config + proxy: vite.config.ts
- Env example: .env.example

--------------------------------------------------------------------------------

## Run Scripts

- `pnpm run dev` → Vite only (frontend)
- `pnpm run dev:server` → Backend only (Express + Socket.IO) using tsx
- `pnpm run dev:all` → Both concurrently (recommended for dev)
- `pnpm run build` → Type-check and build frontend
- `pnpm run preview` → Preview built frontend

--------------------------------------------------------------------------------

## Security & Next Steps

- Centralized pot is simple but risky; move to an on-chain escrow program with PDAs for production.
- Add authoritative ledger for wagers and winnings; cashout must read from server balances, not arbitrary requests.
- Implement robust Ratelimits, authentication for REST, signature checks linking wallet → player ID, and replay protection.
- Anti-cheat: server validates physics and collisions; extend to full Agar mechanics (split, eject mass, pellet spawns).
- Monitoring: add structured logs, error reporting, and transaction indexing or webhooks for better confirm UX.
