export function worldToScreen(
  wx: number,
  wy: number,
  camX: number,
  camY: number,
  width: number,
  height: number,
  scale = 1
) {
  const sx = (wx - camX) * scale + width / 2;
  const sy = (wy - camY) * scale + height / 2;
  return { x: sx, y: sy };
}

export function screenToWorld(
  sx: number,
  sy: number,
  camX: number,
  camY: number,
  width: number,
  height: number,
  scale = 1
) {
  const wx = (sx - width / 2) / scale + camX;
  const wy = (sy - height / 2) / scale + camY;
  return { x: wx, y: wy };
}

export function lamportsToSol(lamports?: number) {
  if (!lamports) return 0;
  return lamports / 1_000_000_000;
}

export type DisplayPlayer = {
  id: string;
  name: string;
  x: number;
  y: number;
  mass: number;
  // Hex color for this player's cells (stable across splits); optional for backward compatibility
  color?: string;
  wagerLamports?: number;
  wagerDollars?: number;
  // Optional server-authoritative Q-hold cashout progress (0..1) for your own blobs
  cashoutHoldProgress?: number;
};

export type DisplayPellet = {
  id: string;
  x: number;
  y: number;
  mass: number;
};

export type DisplayEjected = {
  id: string;
  x: number;
  y: number;
  mass: number;
  color?: string;
  wagerLamports?: number;
  wagerDollars?: number;
};

export type DisplayVirus = {
  id: string;
  x: number;
  y: number;
  mass: number;
  feedCount: number;
  absorptionProgress?: number;
  beingAbsorbedBy?: string;
};
