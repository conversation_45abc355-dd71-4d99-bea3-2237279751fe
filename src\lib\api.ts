export type Health = {
  ok: boolean;
  network: string;
  potAddress: string;
};

/**
 * API base resolution:
 * - If VITE_API_BASE is provided at build/dev time, use it.
 * - During local dev (vite running on :5173) default to http://localhost:8787 where the game server runs.
 * - Otherwise use relative paths so production can use same origin.
 */
const API_BASE = (() => {
  const envBase = (import.meta.env as any).VITE_API_BASE as string | undefined;
  if (envBase && envBase.length > 0) return envBase.replace(/\/$/, "");
  if (typeof window !== "undefined" && window.location?.port === "5173") {
    return "http://localhost:8787";
  }
  return "";
})();

function buildUrl(path: string) {
  if (!API_BASE) return path;
  // ensure leading slash
  return `${API_BASE}${path.startsWith("/") ? path : "/" + path}`;
}

export async function apiGetHealth(): Promise<Health> {
  const res = await fetch(buildUrl("/api/health"));
  if (!res.ok) throw new Error(`health ${res.status}`);
  return res.json();
}

export async function apiGetPotAddress(): Promise<{ potAddress: string }> {
  const res = await fetch(buildUrl("/api/pot-address"));
  if (!res.ok) throw new Error(`pot-address ${res.status}`);
  return res.json();
}

export async function apiWagerInitiate(params: {
  amountLamports: number;
  playerPubkey: string;
  amountDollars?: number;
  solPriceUsd?: number;
}): Promise<{
  joinCode: string;
  potAddress: string;
  amountLamports: number;
  memo?: string;
  expiresAt: number;
}> {
  const res = await fetch(buildUrl("/api/wager/initiate"), {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(params),
  });
  if (!res.ok) {
    const t = await res.text();
    throw new Error(`initiate failed: ${res.status} ${t}`);
  }
  return res.json();
}

export async function apiWagerConfirm(params: {
  joinCode: string;
  signature: string;
}): Promise<{ status: "confirmed"; joinCode: string }> {
  const res = await fetch(buildUrl("/api/wager/confirm"), {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(params),
  });
  if (!res.ok) {
    const t = await res.text();
    throw new Error(`confirm failed: ${res.status} ${t}`);
  }
  return res.json();
}

export async function apiCashout(params: {
  playerPubkey: string;
  amountLamports: number;
}): Promise<{ signature: string }> {
  const res = await fetch(buildUrl("/api/cashout"), {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(params),
  });
  if (!res.ok) {
    const t = await res.text();
    throw new Error(`cashout failed: ${res.status} ${t}`);
  }
  return res.json();
}