import type { TickMsg, WorldPoint } from "@/lib/types";
import GameRenderer from "./Game/Renderer";

type Props = {
  tick: TickMsg | null;
  youId: string | null;
  className?: string;
  solPriceUsd?: number | null;
  onWorldMouseMove?: (world: WorldPoint) => void;
  // admin/debug: show hitbox overlays for players and pellets
  showHitboxes?: boolean;
  // admin/debug: show mass numbers above entities
  showMass?: boolean;
};

export default function GameCanvas(props: Props) {
  return <GameRenderer {...props} />;
}