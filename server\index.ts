import express from "express";
import http from "http";
import cors from "cors";
import dotenv from "dotenv";
import { z } from "zod";
import * as ws from "ws";
const WebSocketServer = (ws as any).WebSocketServer;
import { encode as msgpackEncode, decode as msgpackDecode } from "@msgpack/msgpack";
import { Worker } from "worker_threads";
import path from "path";
import fs from "fs";
import { fileURLToPath } from "url";
import { CONFIG } from "../src/shared/game-config";
import { ConvexHttpClient } from "convex/browser";
import {
  Cluster,
  clusterApiUrl,
  Connection,
  Keypair,
  LAMPORTS_PER_SOL,
  PublicKey,
  sendAndConfirmTransaction,
  SystemProgram,
  Transaction,
  TransactionInstruction,
} from "@solana/web3.js";
import { v4 as uuidv4 } from "uuid";

// Load env files
dotenv.config(); // Load .env
dotenv.config({ path: '.env.local' }); // Load .env.local (Convex URL)

// Initialize Convex client (optional - server can run without it)
let convex: any = null;
try {
  const convexUrl = process.env.VITE_CONVEX_URL;
  if (convexUrl && convexUrl.startsWith('http')) {
    convex = new ConvexHttpClient(convexUrl);
    console.log(`[server] Convex client initialized with URL: ${convexUrl}`);
  } else {
    console.log(`[server] Convex URL not configured or invalid, running without database integration`);
  }
} catch (error) {
  console.warn(`[server] Failed to initialize Convex client:`, error);
}

// Helper function to store game session in database
async function storeGameSession(publicAddress: string, finalStats: any, clientId: string) {
  try {
    console.log(`[server] Storing game session for ${publicAddress}:`, finalStats);
    
    if (convex) {
      try {
        // Try to import the API - this will fail if not generated yet
        const apiModule = await import("../convex/_generated/api.js");
        const api = apiModule.api as any;
        
        // Check if gameSessions API exists
        if (api?.gameSessions?.createCompletedGameSession) {
          // Get player name from the players map or use default
          const playerMeta = players.get(clientId);
          const playerName = playerMeta?.name || "Unknown";
          
          await convex.mutation(api.gameSessions.createCompletedGameSession, {
            publicAddress,
            playerName,
            gameId: clientId,
            wagerStart: finalStats.wagerStart || 0,
            stats: {
              timeAlive: finalStats.timeAlive || 0,
              maxMass: finalStats.maxMass || 20,
              killCount: finalStats.killCount || 0,
              pelletsEaten: finalStats.pelletsEaten || 0,
              ejectedMasses: finalStats.ejectedMasses || 0,
              splits: finalStats.splits || 0,
              startTime: finalStats.startTime || Date.now(),
              endTime: finalStats.endTime,
              finalMass: finalStats.finalMass || 20,
              wagerStart: finalStats.wagerStart || 0,
              wagerEnd: finalStats.wagerEnd || 0,
              gameResult: finalStats.gameResult || "disconnect",
              consumedBy: finalStats.consumedBy || undefined,
            }
          });
          console.log(`[server] Successfully stored game session in database`);
        } else {
          throw new Error("gameSessions API not available");
        }
      } catch (apiError: any) {
        console.warn(`[server] API not available yet, logging data:`, apiError.message);
        // Fallback to logging when API isn't generated yet
        console.log(`[server] Game session data:`, {
          publicAddress,
          gameId: clientId,
          stats: finalStats,
          timestamp: Date.now()
        });
      }
    } else {
      console.log(`[server] No Convex client - logging game session data only`);
      console.log(`[server] Game session data:`, {
        publicAddress,
        gameId: clientId,
        stats: finalStats,
        timestamp: Date.now()
      });
    }
  } catch (error) {
    console.error("[server] Error storing game session:", error);
  }
}

// --- Config ---
const PORT = Number(process.env.PORT || 8787);
const SOLANA_NETWORK = (process.env.SOLANA_NETWORK as Cluster) || "devnet";
const POT_PRIVATE_KEY = process.env.POT_PRIVATE_KEY; // JSON string of secret key array (Uint8Array)
const JOIN_CODE_TTL_MS = 10 * 60 * 1000; // 10 minutes
const TICK_RATE = 60; // 30 updates per second

// Cashout + fee config
// Platform fee as basis points (bps). Default 5% => 500 bps.
const PLATFORM_FEE_BPS = Number.parseInt(process.env.PLATFORM_FEE_BPS || "500");
// Estimated network fee (lamports) to subtract from player's payout so pot isn't subsidizing it.
// This is an estimate; real fee will be paid by the fee payer (pot) when submitting tx.
const NETWORK_FEE_LAMPORTS_EST = Number.parseInt(process.env.NETWORK_FEE_LAMPORTS_EST || "5000");
// Destination for the 5% platform fee (public key string)
const PLATFORM_WALLET = process.env.PLATFORM_WALLET || "";

// --- Solana setup ---
const connection = new Connection(clusterApiUrl(SOLANA_NETWORK), "confirmed");

function parseKeypairFromEnv(json: string | undefined): Keypair {
  if (!json) {
    console.warn(
      "[WARN] No POT_PRIVATE_KEY provided. Generating a temporary in-memory keypair. DO NOT USE IN PROD."
    );
    return Keypair.generate();
  }
  try {
    // Expecting a JSON array string like: "[12,34, ...]"
    const arr = JSON.parse(json) as number[];
    const secret = new Uint8Array(arr);
    return Keypair.fromSecretKey(secret);
  } catch (e) {
    console.error(
      "[ERROR] Failed to parse POT_PRIVATE_KEY as JSON Uint8Array. Provide a JSON array string for the secret key."
    );
    throw e;
  }
}

const potKeypair = parseKeypairFromEnv(POT_PRIVATE_KEY);
const potPublicKey = potKeypair.publicKey;

// Parse platform wallet (optional in dev)
let platformPublicKey: PublicKey | null = null;
try {
  if (PLATFORM_WALLET && PLATFORM_WALLET.length > 0) {
    platformPublicKey = new PublicKey(PLATFORM_WALLET);
  }
} catch (e) {
  console.warn("[WARN] Invalid PLATFORM_WALLET provided; platform fee transfers will be skipped.");
  platformPublicKey = null;
}

// --- Minimal in-memory wager state ---
type PendingWager = {
  joinCode: string;
  playerPubkey: string;
  amountLamports: number;
  amountDollars?: number;
  solPriceUsd?: number;
  status: "awaiting_transfer" | "confirmed" | "expired";
  createdAt: number;
};

const pendingWagers = new Map<string, PendingWager>();
const confirmedJoinCodes = new Set<string>();

// Cleanup expired join codes on an interval
setInterval(() => {
  const now = Date.now();
  for (const [code, w] of pendingWagers.entries()) {
    if (w.status !== "confirmed" && now - w.createdAt > JOIN_CODE_TTL_MS) {
      w.status = "expired";
      pendingWagers.delete(code);
    }
  }
}, 30_000);

// --- Express app ---
const app = express();
app.use(
  cors({
    origin: "*", // tighten in production
  })
);
app.use(express.json());

app.get("/api/health", (_req, res) => {
  res.json({
    ok: true,
    network: SOLANA_NETWORK,
    potAddress: potPublicKey.toBase58(),
    worldSize: CONFIG.WORLD_SIZE,
    tickRate: CONFIG.TICK_RATE,
  });
});

app.get("/api/pot-address", (_req, res) => {
  res.json({ potAddress: potPublicKey.toBase58() });
});

// Expose current authoritative game config so clients/tools can verify/compare at runtime
app.get("/api/game-config", (_req, res) => {
  res.json(CONFIG);
});

const initiateSchema = z.object({
  amountLamports: z.number().int().positive().max(1_000_000_000_000),
  playerPubkey: z.string().min(32),
  amountDollars: z.number().positive().optional(),
  solPriceUsd: z.number().positive().optional(),
});

app.post("/api/wager/initiate", (req, res) => {
  const parse = initiateSchema.safeParse(req.body);
  if (!parse.success) {
    return res.status(400).json({ error: "Invalid body", issues: parse.error.issues });
  }
  const { amountLamports, playerPubkey, amountDollars, solPriceUsd } = parse.data;
  // Create a join code reservation
  const joinCode = uuidv4();
  const record: PendingWager = {
    joinCode,
    playerPubkey,
    amountLamports,
    amountDollars,
    solPriceUsd,
    status: "awaiting_transfer",
    createdAt: Date.now(),
  };
  pendingWagers.set(joinCode, record);

  res.json({
    joinCode,
    potAddress: potPublicKey.toBase58(),
    amountLamports,
    // Optional: recommend including the joinCode in the transaction memo from client wallet for easier indexing
    memo: `JOIN:${joinCode}`,
    expiresAt: record.createdAt + JOIN_CODE_TTL_MS,
  });
});

const confirmSchema = z.object({
  joinCode: z.string().uuid(),
  signature: z.string().min(40),
});

app.post("/api/wager/confirm", async (req, res) => {
  const parse = confirmSchema.safeParse(req.body);
  if (!parse.success) {
    return res.status(400).json({ error: "Invalid body", issues: parse.error.issues });
  }
  const { joinCode, signature } = parse.data;
  const record = pendingWagers.get(joinCode);
  if (!record) {
    return res.status(404).json({ error: "Join code not found or expired" });
  }
  if (record.status === "confirmed") {
    return res.json({ status: "confirmed", joinCode });
  }
  if (Date.now() - record.createdAt > JOIN_CODE_TTL_MS) {
    pendingWagers.delete(joinCode);
    return res.status(410).json({ error: "Join code expired" });
  }

  try {
    // Server-side polling: wait a short window for the transaction to be indexed/confirmed
    // This avoids forcing the client to poll repeatedly.
    const maxAttempts = 15;
    const delayMs = 1000;
    let parsedTx: any = null;
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      parsedTx = await connection.getParsedTransaction(signature, {
        maxSupportedTransactionVersion: 0,
        commitment: "confirmed",
      } as any);
      if (parsedTx) break;
      // wait before retrying
      await new Promise((r) => setTimeout(r, delayMs));
    }

    if (!parsedTx) {
      // Still not found after server-side waiting — surface a clear error so client can fallback to retry later
      return res.status(400).json({ error: "Transaction not found/confirmed yet" });
    }

    // Look for a SystemProgram transfer to pot address
    const found = (parsedTx.transaction.message.instructions as any[]).some((ix: any) => {
      return (
        ix?.program === "system" &&
        ix?.parsed?.type === "transfer" &&
        ix?.parsed?.info?.destination === potPublicKey.toBase58() &&
        Number(ix?.parsed?.info?.lamports ?? 0) >= record.amountLamports
      );
    });

    if (!found) {
      return res.status(400).json({ error: "Required transfer to pot not found in transaction" });
    }

    // Optional: verify source matches provided playerPubkey
    const sourceOk = (parsedTx.transaction.message.instructions as any[]).some((ix: any) => {
      return (
        ix?.program === "system" &&
        ix?.parsed?.type === "transfer" &&
        ix?.parsed?.info?.source === record.playerPubkey
      );
    });

    if (!sourceOk) {
      // Not strictly required for MVP, but safer
      console.warn("[WARN] Confirmed transfer did not match expected source player pubkey");
    }

    record.status = "confirmed";
    confirmedJoinCodes.add(joinCode);
    res.json({ status: "confirmed", joinCode });
  } catch (e: any) {
    console.error("confirm error", e);
    res.status(500).json({ error: "Failed to confirm wager transfer", details: String(e?.message || e) });
  }
});

const cashoutSchema = z.object({
  playerPubkey: z.string().min(32),
  amountLamports: z.number().int().positive().max(10 * LAMPORTS_PER_SOL), // dev safety cap
});

app.post("/api/cashout", async (req, res) => {
  const parse = cashoutSchema.safeParse(req.body);
  if (!parse.success) {
    return res.status(400).json({ error: "Invalid body", issues: parse.error.issues });
  }
  const { playerPubkey, amountLamports } = parse.data;

  try {
    const toPubkey = new PublicKey(playerPubkey);
    
    // Get recent blockhash and create transaction with proper fee payer
    const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash();
    
    const ix = SystemProgram.transfer({
      fromPubkey: potPublicKey,
      toPubkey,
      lamports: amountLamports,
    });
    
    const tx = new Transaction({
      blockhash,
      lastValidBlockHeight,
      feePayer: potPublicKey, // Explicitly set fee payer
    }).add(ix);
    
    const sig = await sendAndConfirmTransaction(connection, tx, [potKeypair], {
      commitment: "confirmed",
      skipPreflight: false, // Enable preflight to catch simulation errors
    });

    res.json({ signature: sig });
  } catch (e: any) {
    console.error("cashout error", e);
    res.status(500).json({ error: "Cashout failed", details: String(e?.message || e) });
  }
});

// --- HTTP server + Socket.IO ---
const server = http.createServer(app);
// WebSocket server (faster, leaner than socket.io)
// Prefer the Server export (ws.Server). Fallbacks try common shapes without picking the client constructor.
const WebSocketServerClass =
  (ws as any).Server ?? (ws as any).WebSocketServer ?? (ws as any).default?.Server ?? null;
if (!WebSocketServerClass) {
  throw new Error("Unable to locate WebSocket Server constructor from 'ws' package");
}
const wss = new WebSocketServerClass({ server, path: "/ws" });
const wsClients = new Map<string, any>(); // id -> ws
// Map WS client id -> player's wallet pubkey (from wager confirm flow)
const clientWallets = new Map<string, string>();

// Worker management and hot-reload (dev)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

let gameWorker: Worker | null = null;

function spawnWorker() {
  try {
    const isProd = process.env.NODE_ENV === "production";
    if (!isProd) {
      // Dev: run TS worker through tsx loader
      const tsWorkerUrl = new URL("./game-worker.ts", import.meta.url);
      gameWorker = new Worker(tsWorkerUrl, {
        execArgv: ["--import", "tsx"],
      });
    } else {
      // Prod: prefer bundled JS worker
      const jsWorkerPath = path.resolve(__dirname, "..", "game-worker.js");
      if (!fs.existsSync(jsWorkerPath)) {
        console.warn("[worker] bundled JS not found at", jsWorkerPath, "— attempting to load TS via tsx loader");
        const tsWorkerUrl = new URL("../game-worker.ts", import.meta.url);
        gameWorker = new Worker(tsWorkerUrl, {
          execArgv: ["--import", "tsx"],
        });
      } else {
        gameWorker = new Worker(jsWorkerPath, { execArgv: [] });
      }
    }

    if (!gameWorker) {
      console.error("[worker] failed to create worker instance");
      return;
    }

    gameWorker.on("error", (err) => {
      console.error("[worker] error", err);
    });
    gameWorker.on("exit", (code) => {
      console.warn("[worker] exited with code", code);
    });

    // Attach message forwarding handlers here
    gameWorker.on("message", (m: any) => {
      try {
        if (!m || typeof m !== "object") return;
        if (m.type === "tick") {
          const perClient = m.perClient || {};
          for (const clientId of Object.keys(perClient)) {
            const payload = perClient[clientId];
            const clientWs = wsClients.get(clientId);
            if (!clientWs || (clientWs as any).readyState !== (clientWs as any).OPEN) continue;
            try {
              if ((clientWs as any).bufferedAmount > 128 * 1024) continue;
            } catch {}
            try {
              clientWs.send(msgpackEncode({ type: "tick", payload }));
            } catch {}
          }
        } else if (m.type === "join_ack") {
          console.log(`[server] worker join_ack for client=${m.clientId}`);
          const clientWs = wsClients.get(m.clientId);
          if (clientWs && (clientWs as any).readyState === (clientWs as any).OPEN) {
            try {
              clientWs.send(msgpackEncode({ type: "join_ack", payload: { ok: true, you: m.you } }));
            } catch {}
          }
        } else if (m.type === "cashout_ready") {
          (async () => {
            const clientId: string = m.clientId;
            const amountDollars: number = Math.max(0, Number(m.amountDollars || 0));
            const clientWs = wsClients.get(clientId);
            if (!clientId || !Number.isFinite(amountDollars) || amountDollars <= 0) return;
            
            // Get the player's SOL price from their wager record to convert dollars back to lamports
            let solPriceUsd = 50; // fallback price
            for (const [joinCode, wagerRec] of pendingWagers.entries()) {
              if (clientWallets.get(clientId) === wagerRec.playerPubkey && wagerRec.solPriceUsd) {
                solPriceUsd = wagerRec.solPriceUsd;
                break;
              }
            }
            const grossLamports = Math.floor((amountDollars / solPriceUsd) * 1_000_000_000); // Convert dollars to lamports

            const toAddr = clientWallets.get(clientId);
            if (!toAddr) {
              if (clientWs && (clientWs as any).readyState === (clientWs as any).OPEN) {
                try {
                  clientWs.send(msgpackEncode({
                    type: "cashout_result",
                    payload: { ok: false, error: "No player wallet available for payout" }
                  }));
                } catch {}
              }
              try {
                gameWorker?.postMessage({ type: "cashout_reset", clientId });
              } catch {}
              return;
            }

            try {
              const playerPubkey = new PublicKey(toAddr);
              const feeBps = Number.isFinite(PLATFORM_FEE_BPS) ? PLATFORM_FEE_BPS : 500;
              const rawPlatformFeeLamports = Math.floor((grossLamports * Math.max(0, feeBps)) / 10_000);
              // Only deduct/send platform fee when a valid platform wallet is configured
              const platformFeeLamports = platformPublicKey ? rawPlatformFeeLamports : 0;
              const networkFeeLamports = Number.isFinite(NETWORK_FEE_LAMPORTS_EST) ? Math.max(0, NETWORK_FEE_LAMPORTS_EST) : 5000;
              const playerNetLamports = Math.max(0, grossLamports - platformFeeLamports - networkFeeLamports);

              const ixs: TransactionInstruction[] = [];
              if (playerNetLamports > 0) {
                ixs.push(SystemProgram.transfer({
                  fromPubkey: potPublicKey,
                  toPubkey: playerPubkey,
                  lamports: playerNetLamports,
                }));
              }
              if (platformFeeLamports > 0 && platformPublicKey) {
                ixs.push(SystemProgram.transfer({
                  fromPubkey: potPublicKey,
                  toPubkey: platformPublicKey,
                  lamports: platformFeeLamports,
                }));
              }

              // If no transfers due to rounding/fees, just reset and notify client
              if (ixs.length === 0) {
                if (clientWs && (clientWs as any).readyState === (clientWs as any).OPEN) {
                  try {
                    clientWs.send(msgpackEncode({
                      type: "cashout_result",
                      payload: {
                        ok: false,
                        error: "Payout after fees is zero",
                        grossLamports,
                        platformFeeLamports,
                        networkFeeLamports,
                        playerNetLamports,
                      }
                    }));
                  } catch {}
                }
                try {
                  gameWorker?.postMessage({ type: "cashout_reset", clientId });
                } catch {}
                return;
              }

              // Get recent blockhash and create transaction with proper fee payer
              const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash();
              
              const tx = new Transaction({
                blockhash,
                lastValidBlockHeight,
                feePayer: potPublicKey, // Explicitly set fee payer
              });
              
              for (const ix of ixs) tx.add(ix);

              const sig = await sendAndConfirmTransaction(connection, tx, [potKeypair], {
                commitment: "confirmed",
                skipPreflight: false, // Enable preflight to catch simulation errors
              });

              // Notify client
              if (clientWs && (clientWs as any).readyState === (clientWs as any).OPEN) {
                try {
                  clientWs.send(msgpackEncode({
                    type: "cashout_result",
                    payload: {
                      ok: true,
                      signature: sig,
                      grossLamports,
                      platformFeeLamports,
                      networkFeeLamports,
                      playerNetLamports,
                    }
                  }));
                } catch {}
              }

              // Tell worker to deduct the gross from player's internal balance
              try {
                gameWorker?.postMessage({ type: "apply_cashout", clientId, amountDollars: amountDollars });
              } catch {}
            } catch (err: any) {
              console.error("[cashout_ready] payout failed", err);
              const clientWs2 = wsClients.get(m.clientId);
              if (clientWs2 && (clientWs2 as any).readyState === (clientWs2 as any).OPEN) {
                try {
                  clientWs2.send(msgpackEncode({
                    type: "cashout_result",
                    payload: { ok: false, error: String(err?.message || err) }
                  }));
                } catch {}
              }
              try {
                gameWorker?.postMessage({ type: "cashout_reset", clientId: m.clientId });
              } catch {}
            }
          })();
        } else if (m.type === "player_cashed_out") {
          // Player has cashed out and been removed from the game
          const clientId: string = m.clientId;
          const amountLamports: number = m.amountLamports || 0;
          const finalStats = m.finalStats;
          const clientWs = wsClients.get(clientId);
          
          // Store game statistics in database if available
          if (finalStats && clientWallets.has(clientId)) {
            const publicAddress = clientWallets.get(clientId);
            if (publicAddress) {
              storeGameSession(publicAddress, finalStats, clientId).catch(console.error);
            }
          }
          
          if (clientWs && (clientWs as any).readyState === (clientWs as any).OPEN) {
            try {
              // Send cashout completion message to client with game stats
              console.log(`[server] Sending cashout_complete with stats:`, finalStats);
              clientWs.send(msgpackEncode({
                type: "cashout_complete",
                payload: {
                  amountLamports,
                  returnToLobby: true,
                  gameStats: finalStats
                }
              }));
            } catch {}
            
            // Close the WebSocket connection to force return to lobby
            setTimeout(() => {
              try {
                clientWs.close();
              } catch {}
            }, 1000); // Give client time to process the message
          }
          
          // Clean up client data
          wsClients.delete(clientId);
          clientWallets.delete(clientId);
        } else if (m.type === "consumed") {
          const targetWs = wsClients.get(m.consumed);
          const finalStats = m.finalStats;
          
          // Store game statistics in database if available
          if (finalStats && clientWallets.has(m.consumed)) {
            const publicAddress = clientWallets.get(m.consumed);
            if (publicAddress) {
              storeGameSession(publicAddress, finalStats, m.consumed).catch(console.error);
            }
          }
          
          if (targetWs && (targetWs as any).readyState === (targetWs as any).OPEN) {
            try {
              targetWs.send(msgpackEncode({
                type: "consumed",
                payload: {
                  by: m.by,
                  gameStats: finalStats
                }
              }));
            } catch {}
            try {
              targetWs.terminate();
            } catch {}
          }
          
          // Clean up client data
          wsClients.delete(m.consumed);
          clientWallets.delete(m.consumed);
        } else if (m.type === "player_disconnected") {
          const clientId: string = m.clientId;
          const finalStats = m.finalStats;
          
          // Store game statistics in database if available
          if (finalStats && clientWallets.has(clientId)) {
            const publicAddress = clientWallets.get(clientId);
            if (publicAddress) {
              storeGameSession(publicAddress, finalStats, clientId).catch(console.error);
            }
          }
          
          // Clean up client data
          wsClients.delete(clientId);
          clientWallets.delete(clientId);
        }
      } catch (e) {
        // ignore worker message handling errors
        console.error("[server] error handling worker message", e);
      }
    });
  } catch (e) {
    console.error("[worker] failed to start:", e);
  }
}

function restartWorker(reason: string) {
  console.log("[worker] restarting:", reason);
  const old = gameWorker;
  gameWorker = null;
  if (old) {
    try {
      old.terminate();
    } catch {}
  }
  spawnWorker();
}

// Start initial worker
spawnWorker();

// Dev hot-reload: restart worker when shared config or worker source changes
if (process.env.NODE_ENV !== "production") {
  const cfgPath = path.resolve(__dirname, "..", "src", "shared", "game-config.ts");
  const workerTsPath = path.resolve(__dirname, "game-worker.ts");
  const debounce = (fn: (p: string) => void, ms = 200) => {
    let t: any;
    return (p: string) => {
      clearTimeout(t);
      t = setTimeout(() => fn(p), ms);
    };
  };
  const onChange = debounce((p) => restartWorker(`file change: ${path.basename(p)}`));
  try {
    fs.watch(cfgPath, () => onChange(cfgPath));
  } catch {}
  try {
    fs.watch(workerTsPath, () => onChange(workerTsPath));
  } catch {}
}

// --- Minimal authoritative game state (MVP skeleton) ---
type Player = {
  id: string;
  name: string;
  x: number;
  y: number;
  vx: number;
  vy: number;
  mass: number;
  wagerLamports: number;
};

const players = new Map<string, Player>();
const inputs = new Map<string, { tx: number; ty: number }>(); // target x,y
 
const WORLD_SIZE = 4000;
const BASE_SPEED = 350; // units per second
const MASS_SPEED_FACTOR = 0.5; // larger mass slows you down
 
// --- Pellets (static food) ---
type Pellet = {
  id: string;
  x: number;
  y: number;
  mass: number;
};
const pellets = new Map<string, Pellet>();
const INITIAL_PELLET_COUNT = 800;
const MAX_PELLETS = 1200;
const PELLET_MASS = 1;
const PELLET_SPAWN_PER_TICK = 2;
 
const CELL_SIZE = 80;
const pelletGrid = new Map<string, Set<string>>();
const pelletCellOf = new Map<string, string>();

function cellKeyFor(x: number, y: number) {
  const cx = Math.floor(x / CELL_SIZE);
  const cy = Math.floor(y / CELL_SIZE);
  return `${cx},${cy}`;
}

function addPellet(id: string, x: number, y: number, mass: number) {
  pellets.set(id, { id, x, y, mass });
  const key = cellKeyFor(x, y);
  pelletCellOf.set(id, key);
  let set = pelletGrid.get(key);
  if (!set) {
    set = new Set<string>();
    pelletGrid.set(key, set);
  }
  set.add(id);
}

function removePellet(id: string) {
  const cell = pelletCellOf.get(id);
  if (cell) {
    const set = pelletGrid.get(cell);
    if (set) {
      set.delete(id);
      if (set.size === 0) pelletGrid.delete(cell);
    }
    pelletCellOf.delete(id);
  }
  pellets.delete(id);
}

function randPosInWorld() {
  return {
    x: (Math.random() - 0.5) * WORLD_SIZE,
    y: (Math.random() - 0.5) * WORLD_SIZE,
  };
}
 
function spawnPellet() {
  const id = uuidv4();
  const pos = randPosInWorld();
  addPellet(id, pos.x, pos.y, PELLET_MASS);
}
 
// initial pellets
for (let i = 0; i < INITIAL_PELLET_COUNT; i++) spawnPellet();
 
wss.on("connection", (ws) => {
  const clientId = uuidv4();
  (ws as any)._id = clientId;
  wsClients.set(clientId, ws);

  // Helper to send msgpack encoded messages (type + payload)
  function send(type: string, payload: any) {
    if (!(ws && (ws as any).readyState === (ws as any).OPEN)) return;
    // Drop if client bufferedAmount looks large (backpressure)
    try {
      if ((ws as any).bufferedAmount > 64 * 1024) return;
    } catch {}
    const msg = { type, payload };
    try {
      ws.send(msgpackEncode(msg));
    } catch (e) {}
  }

  // message handler: expects msgpack-encoded { type, payload }
  ws.on("message", (buf) => {
    let msg: any;
    try {
      msg = msgpackDecode(new Uint8Array(buf as ArrayBufferLike));
    } catch (e) {
      return;
    }
    const { type, payload } = msg || {};
    if (type === "join") {
      try {
        const joinSchema = z.object({
          joinCode: z.string().uuid(),
          tempName: z.string().min(1).max(20),
        });
        const parsed = joinSchema.parse(payload);
        console.log(`[server] WS join request client=${clientId} joinCode=${parsed.joinCode} tempName=${parsed.tempName}`);
        if (!confirmedJoinCodes.has(parsed.joinCode)) {
          console.warn(`[server] join rejected: joinCode not confirmed for client=${clientId}`);
          send("join_ack", { ok: false, error: "Join code not confirmed" });
          return;
        }
        // Record player's wallet from the confirmed wager for payouts
        const wagerRec = pendingWagers.get(parsed.joinCode);
        if (wagerRec?.playerPubkey) {
          clientWallets.set(clientId, wagerRec.playerPubkey);
        }

        // forward join to the game worker (authoritative spawn)
        const wagerLamports = wagerRec?.amountLamports ?? 0;
        const wagerDollars = wagerRec?.amountDollars ?? 0;
        const solPriceUsd = wagerRec?.solPriceUsd ?? 50; // Fallback price
        // If the worker isn't available, explicitly NACK the join so the client doesn't hang.
        if (!gameWorker) {
          console.error("[server] gameWorker not available; rejecting join for client=" + clientId);
          send("join_ack", { ok: false, error: "Worker unavailable" });
          return;
        }
        try {
          gameWorker.postMessage({ type: "join", clientId, tempName: parsed.tempName, wagerLamports, wagerDollars, solPriceUsd });
        } catch (e) {
          // fallback: send immediate ack failure
          console.error("[server] failed to post join to worker", e);
          send("join_ack", { ok: false, error: "Worker unavailable" });
        }
      } catch (e: any) {
        console.error("[server] join parse error", e);
        send("join_ack", { ok: false, error: String(e?.message || e) });
      }
    } else if (type === "input") {
      try {
        const schema = z.object({ tx: z.number(), ty: z.number() });
        const parsed = schema.safeParse(payload);
        if (!parsed.success) return;
        // forward input to worker
        gameWorker?.postMessage({ type: "input", clientId, tx: parsed.data.tx, ty: parsed.data.ty });
      } catch {}
    } else if (type === "eject") {
      // forward eject to worker
      gameWorker?.postMessage({ type: "eject", clientId });
    } else if (type === "split") {
      // forward split to worker
      gameWorker?.postMessage({ type: "split", clientId });
    } else if (type === "cashout_start") {
      // forward start cashout hold to worker
      gameWorker?.postMessage({ type: "cashout_start", clientId });
    } else if (type === "cashout_cancel") {
      // forward cancel cashout hold to worker
      gameWorker?.postMessage({ type: "cashout_cancel", clientId });
    } else if (type === "set_mass") {
      try {
        const schema = z.object({ mass: z.number() });
        const parsed = schema.safeParse(payload);
        if (!parsed.success) return;
        // log and forward mass change request to worker (authoritative)
        console.log(`[server] set_mass request from client=${clientId} mass=${parsed.data.mass}`);
        gameWorker?.postMessage({ type: "set_mass", clientId, mass: parsed.data.mass });
      } catch (e) {
        console.error("[server] set_mass handling error", e);
      }
    } else if (type === "add_balance") {
      try {
        const schema = z.object({ lamports: z.number() });
        const parsed = schema.safeParse(payload);
        if (!parsed.success) return;
        // log and forward balance increase request to worker (authoritative)
        console.log(`[server] add_balance request from client=${clientId} lamports=${parsed.data.lamports}`);
        gameWorker?.postMessage({ type: "add_balance", clientId, lamports: parsed.data.lamports });
      } catch (e) {
        console.error("[server] add_balance handling error", e);
      }
    }
  });

  ws.on("close", () => {
    players.delete(clientId);
    inputs.delete(clientId);
    wsClients.delete(clientId);
    clientWallets.delete(clientId);
  });
});

// Game loop
let lastTick = Date.now();
let tickCounter = 0;

// helper: convert mass -> radius (server-side approximation)
function massToRadius(m: number) {
  // Simple mapping: radius grows with sqrt(mass)
  return Math.max(8, Math.sqrt(m) * 2);
}

/**
 * Simulation moved to worker thread. Main thread receives ticks / events from worker
 * and forwards tick payloads to connected websocket clients.
 */
/**
 * Worker message handlers are attached within spawnWorker().
 * The worker will be hot-restarted in dev when config/worker files change.
 */

// Start server
server.listen(PORT, async () => {
  const bal = await connection.getBalance(potPublicKey).catch(() => 0);
  console.log(
    `[server] Listening on http://localhost:${PORT} | Solana: ${SOLANA_NETWORK} | Pot: ${potPublicKey.toBase58()} (bal: ${bal} lamports)`
  );
  console.log(`[server] WS Path: /ws | REST: /api/*`);
});

/**
 * NOTES:
 * - This is an MVP skeleton. The gameplay loop here is minimal and for demonstration only.
 * - Wager flow (MVP, centralized pot):
 *   1) Client requests /api/wager/initiate with amount + playerPubkey.
 *   2) Server returns joinCode + potAddress.
 *   3) Client sends SOL transfer from their wallet to potAddress.
 *   4) Client calls /api/wager/confirm with joinCode + signature.
 *   5) On success, client can "join" the Socket.IO server with the joinCode.
 * - Cashout is a simple transfer from pot to player (no internal balance checks here yet).
 *   This MUST be replaced by authoritative in-game accounting in production.
 * - POT_PRIVATE_KEY format: JSON array of numbers (secret key). Example (dev only!):
 *   [12,34, ...]  // NEVER commit real keys
 */