import { <PERSON><PERSON><PERSON> } from "buffer";

// Ensure global alias (some libs expect `global` like Node)
if (!(globalThis as any).global) {
 (globalThis as any).global = globalThis;
}
if (typeof window !== "undefined" && !(window as any).global) {
 (window as any).global = window;
}

// Buffer shim
if (!(globalThis as any).Buffer) {
 (globalThis as any).Buffer = Buffer;
}

// process shim with nextTick
if (!(globalThis as any).process) {
 (globalThis as any).process = { env: {}, browser: true } as any;
}

const proc: any = (globalThis as any).process;
if (!proc.env) proc.env = {};
if (proc.browser === undefined) proc.browser = true;
if (typeof proc.nextTick !== "function") {
 proc.nextTick = (cb: (...args: any[]) => void, ...args: any[]) => {
   if (typeof queueMicrotask === "function") {
     queueMicrotask(() => cb(...args));
   } else {
     Promise.resolve().then(() => cb(...args));
   }
 };
}