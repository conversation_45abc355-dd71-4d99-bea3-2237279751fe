import { Button } from "@/components/ui/button";
import { Pencil, PlayCircle } from "lucide-react";

type Props = {
  tempName: string;
  setTempName: (v: string) => void;
  amountSol: string;
  setAmountSol: (v: string) => void;
  confirming: boolean;
  onJoin: () => void;
  joinCode: string | null;
  txSig: string | null;
  solPriceUsd: number | null;
  pubkey: string | null;
};

const quickWagers = [
  { label: "$1", usd: 1 },
  { label: "$3", usd: 3 },
  { label: "$5", usd: 5 },
  { label: "$10", usd: 10 },
];

export default function JoinPanel({
  tempName,
  setTempName,
  amountSol,
  setAmountSol,
  confirming,
  onJoin,
  txSig,
  solPriceUsd,
  pubkey,
}: Props) {
  // Determine if a quick wager matches the currently selected SOL amount.
  const isSelectedQuick = (usd: number) => {
    const amtSol = parseFloat(amountSol);
    if (!solPriceUsd || !isFinite(amtSol)) return false;
    const usdValue = amtSol * solPriceUsd;
    // consider it selected if within 1 cent
    return Math.abs(usdValue - usd) < 0.01;
  };
 
  // fallback mapping in case price is not available (assume ~ $50/SOL as before)
  const fallbackSol = (usd: number) => {
    if (usd === 1) return "0.02";
    if (usd === 3) return "0.06";
    if (usd === 5) return "0.10";
    if (usd === 10) return "0.20";
    return "0.1";
  };
 
  const handleQuickClick = (usd: number) => {
    if (solPriceUsd && solPriceUsd > 0) {
      const sol = (usd / solPriceUsd).toFixed(6);
      setAmountSol(sol);
    } else {
      setAmountSol(fallbackSol(usd));
    }
  };
 
  const amtSolNum = parseFloat(amountSol);
  const hasValidWager = isFinite(amtSolNum) && amtSolNum > 0;
 
  // Require that a quick wager is selected. We consider a quick wager selected either:
  // - when solPriceUsd is available: the current amountSol maps to one of the quick USD amounts (within tolerance),
  // - when price is unavailable: amountSol exactly equals the fallback mapping for a quick amount.
  const hasSelectedQuick = quickWagers.some((q) => {
    if (solPriceUsd && solPriceUsd > 0) {
      return isSelectedQuick(q.usd);
    } else {
      return amountSol === fallbackSol(q.usd);
    }
  });
 
  const canJoin = !!pubkey && hasValidWager && hasSelectedQuick && !confirming;

  return (
    <div className="panel-dark w-full max-w-md p-6 md:p-8 rounded-2xl space-y-4">
      <div>
        <label className="block text-sm text-white/70 mb-2">Login to set your name</label>
        <div className="flex items-center gap-2">
          <input
            className="w-full rounded-md border border-neutral-700 bg-neutral-900 px-3 py-2 text-sm outline-none focus:ring-2 focus:ring-amber-500/50"
            value={tempName}
            onChange={(e) => setTempName(e.target.value)}
            maxLength={20}
            disabled={!pubkey}
            title={!pubkey ? "Connect wallet to set your name" : undefined}
          />
          <Button variant="outline" size="icon" className="border-neutral-700" disabled={!pubkey}>
            <Pencil className="size-4" />
          </Button>
        </div>
      </div>

      <div className="flex items-center gap-3">
        {quickWagers.map((q) => (
          <Button
            key={q.label}
            variant="outline"
            className={`flex-1 min-w-0 text-center border-neutral-700 ${isSelectedQuick(q.usd) ? "bg-neutral-800" : ""}`}
            onClick={() => handleQuickClick(q.usd)}
          >
            {q.label}
          </Button>
        ))}
      </div>

      <Button
        className="w-full gap-2"
        variant="gold"
        size="xl"
        onClick={() => {
          if (!canJoin) {
            if (!pubkey) return alert("Connect wallet first");
            if (!hasValidWager) return alert("Select a wager before joining");
            if (!hasSelectedQuick) return alert("Select a quick wager (use the quick buttons) before joining");
            return;
          }
          onJoin();
        }}
        disabled={!canJoin}
      >
        <PlayCircle className="size-5" />
        {confirming ? (txSig ? "Waiting for confirmation…" : "Sending…") : "JOIN GAME"}
      </Button>

      {!pubkey && <div className="text-xs text-amber-300">You must connect your wallet before joining.</div>}
      {pubkey && !hasValidWager && <div className="text-xs text-white/50">Select a wager (use the quick buttons) before joining.</div>}


    </div>
  );
}