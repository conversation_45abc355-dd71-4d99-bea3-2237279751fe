import { useCallback, useEffect, useRef, useState } from "react";

/**
 * useSolPrice
 * - Default: polls Jupiter Lite Price API V3 for SOL price.
 * - Returns { solPriceUsd, refresh } where refresh re-fetches immediately.
 *
 * Notes:
 * - Uses the SOL mint id required by Jupiter API.
 * - If the Jupiter request fails, the hook leaves the previous value unchanged.
 */
const SOL_MINT = "So11111111111111111111111111111111111111112";
const JUP_LITE_URL = `https://lite-api.jup.ag/price/v3?ids=${SOL_MINT}`;

export default function useSolPrice(pollIntervalMs = 60000) {
  const [solPriceUsd, setSolPriceUsd] = useState<number | null>(null);
  const abortRef = useRef<AbortController | null>(null);

  const fetchPrice = useCallback(async () => {
    try {
      // abort any previous in-flight request
      abortRef.current?.abort();
      const ac = new AbortController();
      abortRef.current = ac;

      const res = await fetch(JUP_LITE_URL, { signal: ac.signal });
      if (!res.ok) return;
      const j = await res.json();

      const data = j?.[SOL_MINT];
      const p = data?.usdPrice;
      if (typeof p === "number") {
        setSolPriceUsd(p);
      }
    } catch {
      // keep previous value on error
    }
  }, []);

  useEffect(() => {
    fetchPrice();
    const id = setInterval(fetchPrice, pollIntervalMs);
    return () => {
      clearInterval(id);
      abortRef.current?.abort();
    };
  }, [fetchPrice, pollIntervalMs]);

  return { solPriceUsd, refresh: fetchPrice };
}