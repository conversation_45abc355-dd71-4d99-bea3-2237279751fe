import { But<PERSON> } from "@/components/ui/button";
import { LogIn, LogOut } from "lucide-react";

type Props = {
  network: string;
  pubkey: string | null;
  connecting: boolean;
  onLogin: () => void;
  onLogout: () => void;
};

export default function TopBar({ network, pubkey, connecting, onLogin, onLogout }: Props) {
  return (
    <div className="relative z-10 flex items-center justify-between px-6 py-4">
      <div className="text-white/90 font-semibold">Welcome</div>
      <div className="flex items-center gap-3">
        <span className="text-xs text-white/50 hidden sm:block">
          Network: {network}
        </span>
        {pubkey ? (
          <Button size="sm" variant="outline" onClick={onLogout} className="gap-2">
            <LogOut className="size-4" />
            {pubkey.slice(0, 4)}…{pubkey.slice(-4)}
          </Button>
        ) : (
          <Button
            size="sm"
            variant="gold"
            onClick={onLogin}
            disabled={connecting}
            className="gap-2"
          >
            <LogIn className="size-4" />
            {connecting ? "Connecting…" : "Login"}
          </Button>
        )}
      </div>
    </div>
  );
}