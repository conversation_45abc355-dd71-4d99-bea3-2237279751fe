import { worldToScreen } from "../utils";

export function drawGrid(
  ctx: CanvasRenderingContext2D,
  width: number,
  height: number,
  camX: number,
  camY: number,
  scale: number,
  worldSize: number,
  grid: number = 40
) {
  ctx.save();

  ctx.strokeStyle = "rgba(255,255,255,0.06)";
  ctx.lineWidth = 1;

  const minBound = -worldSize / 2;
  const maxBound = worldSize / 2;

  const worldHalfW = width / (2 * scale);
  const worldHalfH = height / (2 * scale);

  const startX = Math.max(minBound, Math.floor((camX - worldHalfW - 50) / grid) * grid);
  const endX = Math.min(maxBound, Math.ceil((camX + worldHalfW + 50) / grid) * grid);
  for (let x = startX; x <= endX; x += grid) {
    const p1 = worldToScreen(x, minBound, camX, camY, width, height, scale);
    const p2 = worldToScreen(x, maxBound, camX, camY, width, height, scale);
    ctx.beginPath();
    ctx.moveTo(p1.x, p1.y);
    ctx.lineTo(p2.x, p2.y);
    ctx.stroke();
  }

  const startY = Math.max(minBound, Math.floor((camY - worldHalfH - 50) / grid) * grid);
  const endY = Math.min(maxBound, Math.ceil((camY + worldHalfH + 50) / grid) * grid);
  for (let y = startY; y <= endY; y += grid) {
    const p1 = worldToScreen(minBound, y, camX, camY, width, height, scale);
    const p2 = worldToScreen(maxBound, y, camX, camY, width, height, scale);
    ctx.beginPath();
    ctx.moveTo(p1.x, p1.y);
    ctx.lineTo(p2.x, p2.y);
    ctx.stroke();
  }

  ctx.restore();
}