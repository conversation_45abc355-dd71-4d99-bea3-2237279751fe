/**
 * Shared game configuration and helpers used by both client and server.
 * Keep values deterministic and serializable.
 */
 
export type GameConfig = {
  WORLD_SIZE: number;
  BASE_SPEED: number;
  MASS_SPEED_FACTOR: number;
  MASS_SPEED_DIVISOR: number;
  INITIAL_PELLET_COUNT: number;
  MAX_PELLETS: number;
  PELLET_MASS: number;
  PELLET_SPAWN_PER_TICK: number;
  CELL_SIZE: number;
  TICK_RATE: number; // worker tick frequency (Hz)
  PLAYER_MIN_RADIUS: number;
  MASS_TO_RADIUS_K: number;
  // Visual growth parameters (controls how mass maps to the visual "scale")
  GROW_BASE_MASS: number;
  GROW_EXPONENT: number;
  GROW_CAP: number;
  // Camera / zoom parameters (how the camera zooms out as player mass increases)
  CAMERA_BASE_ZOOM: number;
  CAMERA_MASS_DIVISOR: number;
  CAMERA_MASS_ZOOM_CAP: number;
  CAMERA_MIN_SCALE: number;

  // Abilities - Eject (W)
  EJECT_MIN: number;        // minimum mass ejected
  EJECT_MAX: number;        // maximum mass ejected
  EJECT_FRACTION: number;   // fraction of current mass to eject
  EJECT_BOOST: number;      // how far in front of the player the ejection spawns (world units)
  EJECT_LAUNCH_FACTOR: number; // scales initial ejected mass launch speed relative to split-like speed (0..1)
  EJECT_PELLET_SPEED: number;    // initial speed for ejected pellet projectiles
  EJECT_PELLET_DECEL: number;    // multiplicative velocity decay per second (0..1)
  EJECT_EXTRA_DAMPING: number;   // extra damping per second applied only to ejected masses (0..1); higher -> slows less
  // Target travel distance for ejected pellets (world units). Server computes launch speed to approximate this distance.
  EJECT_TRAVEL_DISTANCE: number;
  // Wager mechanic: fixed lamports deducted per ejection (per emitted blob)
  // Set to the lamports equivalent of $0.02 in your deployment (e.g., via config/env).
  EJECT_WAGER_LAMPORTS: number;

  // Abilities - Split (Space)
  SPLIT_FRACTION: number;   // fraction of mass to split off
  SPLIT_BOOST: number;      // spawn offset for split pellet
  SPLIT_MIN_CELL_MASS: number; // minimum mass per cell after a split

  // Multi-cell
  MAX_CELLS: number;            // max cells per player
  MERGE_COOLDOWN_MS: number;    // time until same-owner cells can merge

  // Viruses
  INITIAL_VIRUS_COUNT: number;  // initial number of viruses spawned
  MAX_VIRUSES: number;          // maximum viruses in the world
  VIRUS_MASS: number;           // base mass of a virus
  VIRUS_FEED_REQUIREMENT: number; // number of ejected masses needed to split a virus
  VIRUS_SPLIT_MASS_THRESHOLD: number; // minimum player mass to split when absorbing virus
  VIRUS_SPAWN_PER_TICK: number; // viruses spawned per tick to maintain density

};

export const CONFIG: GameConfig = {
  WORLD_SIZE: 4000,
  BASE_SPEED: 200,
  MASS_SPEED_FACTOR: 0.1,
  MASS_SPEED_DIVISOR: 30,
  INITIAL_PELLET_COUNT: 10,
  MAX_PELLETS: 1200,
  PELLET_MASS: 2,
  PELLET_SPAWN_PER_TICK: 0.5,
  CELL_SIZE: 80,
  TICK_RATE: 60,
  PLAYER_MIN_RADIUS: 8 * 4,
  MASS_TO_RADIUS_K: 2 * 4,
  // Visual growth defaults
  GROW_BASE_MASS: 12 * 8,
  GROW_EXPONENT: 0.24 * 8,
  GROW_CAP: 2.2 * 8,
  // Camera / zoom defaults
  CAMERA_BASE_ZOOM: 1.5,
  CAMERA_MASS_DIVISOR: 300,
  CAMERA_MASS_ZOOM_CAP: 1.2,
  CAMERA_MIN_SCALE: 0.7,

  // Abilities - Eject (W)
  EJECT_MIN: 0.5,
  EJECT_MAX: 5,
  EJECT_FRACTION: 0.03,
  EJECT_BOOST: 44,
  EJECT_LAUNCH_FACTOR: 0.9,
  // Approximate target travel distance (world units) for ejected pellets.
  // The server computes the launch speed to meet this distance regardless of ejected mass.
  EJECT_TRAVEL_DISTANCE: 600,
  // Fixed wager per ejected blob (lamports). Set for ~$0.02.
  // Example placeholder: 100000 lamports (~0.0001 SOL). Adjust to your SOL/USD price.
  EJECT_WAGER_LAMPORTS: 100000,

  // Abilities - Split (Space)
  SPLIT_FRACTION: 0.25,
  SPLIT_BOOST: 1128,
  SPLIT_MIN_CELL_MASS: 10,


  // Multi-cell
  MAX_CELLS: 16,
  MERGE_COOLDOWN_MS: 7000,

  // Ejected pellets as projectiles
  EJECT_PELLET_SPEED: 650,
  EJECT_PELLET_DECEL: 0.9,
  EJECT_EXTRA_DAMPING: 0.6,

  // Viruses
  INITIAL_VIRUS_COUNT: 15,      // initial number of viruses spawned
  MAX_VIRUSES: 25,              // maximum viruses in the world
  VIRUS_MASS: 100,              // base mass of a virus
  VIRUS_FEED_REQUIREMENT: 7,    // number of ejected masses needed to split a virus
  VIRUS_SPLIT_MASS_THRESHOLD: 120, // minimum player mass to split when absorbing virus
  VIRUS_SPAWN_PER_TICK: 0.1,    // viruses spawned per tick to maintain density
};

/**
 * Convert mass to world-space radius. Must be consistent across server and client.
 */
export function massToRadius(m: number, cfg: GameConfig = CONFIG) {
  return Math.max(cfg.PLAYER_MIN_RADIUS, Math.sqrt(m) * cfg.MASS_TO_RADIUS_K);
}

/**
 * Player speed curve as a function of mass. Keep in sync across server/client.
 */
export function speedForMass(m: number, cfg: GameConfig = CONFIG) {
  // Speed falls off as mass increases.
  // Use a stronger, more intuitive falloff so increasing mass has a clearer effect.
  // New formula:
  //   speed = BASE_SPEED / (1 + (m / MASS_SPEED_DIVISOR) * MASS_SPEED_FACTOR)
  // This makes MASS_SPEED_FACTOR and MASS_SPEED_DIVISOR direct tuning knobs:
  // - Increasing MASS_SPEED_FACTOR -> stronger slowdown with mass
  // - Increasing MASS_SPEED_DIVISOR -> weaker slowdown (mass needs to be larger)
  const massTerm = (m / cfg.MASS_SPEED_DIVISOR) * cfg.MASS_SPEED_FACTOR;
  return cfg.BASE_SPEED / (1 + massTerm);
}

export default CONFIG;