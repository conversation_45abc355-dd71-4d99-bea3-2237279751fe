import { createPortal } from "react-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, Trophy, Clock, Target, TrendingUp, ArrowUpRight, GamepadIcon, Coins, CheckCircle, XCircle, ExternalLink } from "lucide-react";
import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import SizeOverTimeGraph from "./SizeOverTimeGraph";

type ActivityHistoryModalProps = {
  isOpen: boolean;
  onClose: () => void;
  publicAddress?: string;
};

type ActivityItem = {
  id: string;
  type: 'game' | 'transaction';
  timestamp: number;
  data: any;
};

export default function ActivityHistoryModal({
  isOpen,
  onClose,
  publicAddress,
}: ActivityHistoryModalProps) {
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'games' | 'transactions'>('all');
  const [timeRange, setTimeRange] = useState<"24h" | "7d" | "30d" | "all">("7d");
  
  // Query game sessions
  const gameSessions = useQuery(
    (api as any).gameSessions?.getUserGameSessions,
    publicAddress ? { publicAddress, limit: 50 } : "skip"
  );

  // Query transactions
  const transactions = useQuery(
    (api as any).transactions.getTransactionsByUser,
    publicAddress ? { publicAddress, limit: 50 } : "skip"
  );

  // Query user stats
  const userStats = useQuery(
    (api as any).gameSessions?.getUserStatsSummary,
    publicAddress ? { publicAddress } : "skip"
  );

  // Query transaction stats
  const transactionStats = useQuery(
    (api as any).transactions.getTransactionStats,
    publicAddress ? { publicAddress, timeRange } : "skip"
  );

  if (!isOpen) return null;

  // Combine and sort activities by timestamp
  const getActivities = (): ActivityItem[] => {
    const activities: ActivityItem[] = [];

    // Add game sessions
    if (gameSessions) {
      gameSessions.forEach((session: any) => {
        activities.push({
          id: `game-${session._id}`,
          type: 'game',
          timestamp: session.createdAt,
          data: session
        });
      });
    }

    // Add transactions
    if (transactions) {
      transactions.forEach((tx: any) => {
        activities.push({
          id: `tx-${tx._id}`,
          type: 'transaction',
          timestamp: tx.createdAt,
          data: tx
        });
      });
    }

    // Filter by type
    const filtered = activities.filter(activity => {
      if (filter === 'all') return true;
      if (filter === 'games') return activity.type === 'game';
      if (filter === 'transactions') return activity.type === 'transaction';
      return true;
    });

    // Sort by timestamp (newest first)
    return filtered.sort((a, b) => b.timestamp - a.timestamp);
  };

  const activities = getActivities();

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getGameResultColor = (result: string) => {
    switch (result) {
      case 'cashout':
        return 'text-green-400';
      case 'consumed':
        return 'text-red-400';
      case 'disconnect':
        return 'text-yellow-400';
      default:
        return 'text-white/60';
    }
  };

  const getGameResultIcon = (result: string) => {
    switch (result) {
      case 'cashout':
        return <Trophy className="w-4 h-4" />;
      case 'consumed':
        return <Target className="w-4 h-4" />;
      case 'disconnect':
        return <X className="w-4 h-4" />;
      default:
        return null;
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "withdraw":
        return <ArrowUpRight className="w-4 h-4 text-red-400" />;
      case "join_lobby":
        return <GamepadIcon className="w-4 h-4 text-blue-400" />;
      case "cashout":
        return <Coins className="w-4 h-4 text-yellow-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getTransactionStatusIcon = (status: string) => {
    switch (status) {
      case "confirmed":
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case "failed":
        return <XCircle className="w-4 h-4 text-red-400" />;
      case "pending":
        return <Clock className="w-4 h-4 text-yellow-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getTransactionStatusColor = (status: string) => {
    switch (status) {
      case "confirmed":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      case "failed":
        return "bg-red-500/20 text-red-400 border-red-500/30";
      case "pending":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const formatTransactionType = (type: string) => {
    switch (type) {
      case "join_lobby":
        return "Join Game";
      case "cashout":
        return "Cashout";
      case "withdraw":
        return "Withdraw";
      default:
        return type;
    }
  };

  const renderGameActivity = (activity: ActivityItem) => {
    const session = activity.data;
    return (
      <div
        key={activity.id}
        className="bg-neutral-900 rounded-lg p-4 border border-neutral-700 hover:border-neutral-600 transition-colors"
      >
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1 text-blue-400">
              <GamepadIcon className="w-4 h-4" />
              <span className="font-medium text-sm">Game Session</span>
            </div>
            <span className="text-white/40">•</span>
            <div className={`flex items-center gap-1 ${getGameResultColor(session.stats.gameResult)}`}>
              {getGameResultIcon(session.stats.gameResult)}
              <span className="font-medium capitalize text-sm">{session.stats.gameResult}</span>
            </div>
            <span className="text-white/40">•</span>
            <span className="text-white/60 text-sm">{formatDate(session.createdAt)}</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1 text-white/60">
              <Clock className="w-4 h-4" />
              <span className="text-sm">{formatTime(session.stats.timeAlive)}</span>
            </div>
            {session.stats.sizeHistory && session.stats.sizeHistory.length > 0 && (
              <button
                onClick={() => setSelectedSessionId(
                  selectedSessionId === session._id ? null : session._id
                )}
                className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-colors ${
                  selectedSessionId === session._id
                    ? 'bg-blue-600 text-white'
                    : 'bg-neutral-700 hover:bg-neutral-600 text-white/80'
                }`}
                title="View size graph"
              >
                <TrendingUp className="w-3 h-3" />
                Graph
              </button>
            )}
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="flex justify-between">
            <span className="text-white/70">Max Mass:</span>
            <span className="text-white font-medium">{Math.round(session.stats.maxMass)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-white/70">Final Mass:</span>
            <span className="text-white font-medium">{Math.round(session.stats.finalMass)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-white/70">Kills:</span>
            <span className="text-white font-medium">{session.stats.killCount}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-white/70">Pellets:</span>
            <span className="text-white font-medium">{session.stats.pelletsEaten}</span>
          </div>
        </div>

        {session.stats.gameResult === 'consumed' && session.stats.consumedBy && (
          <div className="mt-2 text-sm text-red-400">
            Consumed by player {session.stats.consumedBy.slice(0, 8)}...
          </div>
        )}

        {/* Size over time graph */}
        {selectedSessionId === session._id && session.stats.sizeHistory && (
          <div className="mt-4 pt-4 border-t border-neutral-700">
            <SizeOverTimeGraph gameStats={session.stats} />
          </div>
        )}
      </div>
    );
  };

  const renderTransactionActivity = (activity: ActivityItem) => {
    const tx = activity.data;
    return (
      <div
        key={activity.id}
        className="bg-neutral-900 rounded-lg p-4 border border-neutral-700 hover:border-neutral-600 transition-colors"
      >
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-3">
            {getTransactionIcon(tx.type)}
            <div>
              <div className="font-medium text-white text-sm">
                {formatTransactionType(tx.type)}
              </div>
              <div className="text-xs text-white/60">
                {formatDate(tx.createdAt)}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={`text-xs ${getTransactionStatusColor(tx.status)}`}>
              <div className="flex items-center gap-1">
                {getTransactionStatusIcon(tx.status)}
                {tx.status}
              </div>
            </Badge>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="text-right">
            <div className="font-semibold text-white">
              {tx.amountSol.toFixed(4)} SOL
            </div>
            {tx.amountUsd && (
              <div className="text-xs text-white/60">
                ≈ ${tx.amountUsd.toFixed(2)} USD
              </div>
            )}
          </div>
          {tx.signature && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const explorerUrl = `https://solscan.io/tx/${tx.signature}?cluster=${tx.blockchainNetwork || 'devnet'}`;
                window.open(explorerUrl, '_blank');
              }}
              className="text-xs border-neutral-600 hover:border-neutral-500"
            >
              <ExternalLink className="w-3 h-3 mr-1" />
              View
            </Button>
          )}
        </div>

        {/* Additional details for specific transaction types */}
        {tx.type === "withdraw" && tx.metadata?.recipientAddress && (
          <div className="mt-2 pt-2 border-t border-neutral-700">
            <div className="text-xs text-white/60">
              To: {tx.metadata.recipientAddress.slice(0, 8)}...{tx.metadata.recipientAddress.slice(-8)}
            </div>
          </div>
        )}
        
        {tx.type === "join_lobby" && tx.joinCode && (
          <div className="mt-2 pt-2 border-t border-neutral-700">
            <div className="text-xs text-white/60">
              Game: {tx.joinCode}
            </div>
          </div>
        )}

        {tx.type === "cashout" && tx.metadata?.platformFee && (
          <div className="mt-2 pt-2 border-t border-neutral-700">
            <div className="text-xs text-white/60">
              Platform Fee: {(tx.metadata.platformFee / 1_000_000_000).toFixed(4)} SOL
            </div>
          </div>
        )}
      </div>
    );
  };

  const modal = (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/70" onClick={onClose} />
      <div className="relative z-10 w-full max-w-5xl px-4 max-h-[90vh] overflow-hidden">
        <div className="panel-dark rounded-2xl p-6 md:p-8 shadow-xl">
          <div className="flex items-center justify-between mb-6">
            <div className="text-white/80 font-semibold text-xl">Activity History</div>
            <button
              className="text-white/60 hover:text-white"
              onClick={onClose}
              title="Close"
            >
              <X className="size-5" />
            </button>
          </div>

          {/* Filter Controls */}
          <div className="flex flex-wrap items-center gap-4 mb-6">
            <div className="flex gap-2">
              {(['all', 'games', 'transactions'] as const).map((filterType) => (
                <Button
                  key={filterType}
                  variant={filter === filterType ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilter(filterType)}
                  className="text-xs capitalize"
                >
                  {filterType === 'all' ? 'All Activity' : filterType}
                </Button>
              ))}
            </div>
            <div className="flex gap-2">
              {(["24h", "7d", "30d", "all"] as const).map((range) => (
                <Button
                  key={range}
                  variant={timeRange === range ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTimeRange(range)}
                  className="text-xs"
                >
                  {range === "all" ? "All Time" : range}
                </Button>
              ))}
            </div>
          </div>

          {/* Combined Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            {userStats && (
              <>
                <div className="bg-neutral-900 rounded-lg p-3 border border-neutral-700">
                  <div className="text-xs text-white/60 mb-1">Total Games</div>
                  <div className="text-lg font-semibold text-blue-400">{userStats.totalGames}</div>
                </div>
                <div className="bg-neutral-900 rounded-lg p-3 border border-neutral-700">
                  <div className="text-xs text-white/60 mb-1">Game Cashouts</div>
                  <div className="text-lg font-semibold text-green-400">{userStats.totalCashouts}</div>
                </div>
              </>
            )}
            {transactionStats && (
              <>
                <div className="bg-neutral-900 rounded-lg p-3 border border-neutral-700">
                  <div className="text-xs text-white/60 mb-1">Transactions</div>
                  <div className="text-lg font-semibold text-purple-400">{transactionStats.total}</div>
                </div>
                <div className="bg-neutral-900 rounded-lg p-3 border border-neutral-700">
                  <div className="text-xs text-white/60 mb-1">Volume (SOL)</div>
                  <div className="text-lg font-semibold text-orange-400">{transactionStats.totalVolume.sol.toFixed(4)}</div>
                </div>
              </>
            )}
          </div>

          {/* Activity List */}
          <div className="max-h-96 overflow-y-auto">
            {activities.length > 0 ? (
              <div className="space-y-3">
                {activities.map((activity) => (
                  activity.type === 'game' 
                    ? renderGameActivity(activity)
                    : renderTransactionActivity(activity)
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-white/40 mb-2">No activity found</div>
                <div className="text-white/60 text-sm">
                  {filter === 'games' && 'Start playing to see your game history here!'}
                  {filter === 'transactions' && 'Make a transaction to see your transaction history here!'}
                  {filter === 'all' && 'Start playing or make transactions to see your activity here!'}
                </div>
              </div>
            )}
          </div>

          {/* Close Button */}
          <div className="mt-6 flex justify-center">
            <Button
              variant="gold"
              onClick={onClose}
              className="px-8"
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  return typeof document !== "undefined" ? createPortal(modal, document.body) : modal;
}