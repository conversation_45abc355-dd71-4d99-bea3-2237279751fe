# Server configuration (Node/Express)
PORT=8787
SOLANA_NETWORK=devnet
# DEV ONLY: Centralized pot wallet secret key as JSON array (Uint8Array).
# NEVER commit real keys. For local testing you can leave this empty to auto-generate a volatile keypair.
# Example (do not use in prod): [12,34, ...]
POT_PRIVATE_KEY=

# Platform fee configuration
# PLATFORM_WALLET=YOUR_WALLET_ADDRESS_HERE
# PLATFORM_FEE_BPS=500  # 500 basis points = 5%

# Frontend (Vite requires VITE_ prefix)
VITE_SOLANA_NETWORK=devnet
# Get a Client ID from https://dashboard.web3auth.io
VITE_WEB3AUTH_CLIENT_ID=REPLACE_WITH_YOUR_WEB3AUTH_CLIENT_ID