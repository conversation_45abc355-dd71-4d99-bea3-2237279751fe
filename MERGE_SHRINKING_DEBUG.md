# Cell Merge Shrinking Issue - Debug & Fix

## Problem Description
Sometimes after a merge takes place, the absorbing player (the larger cell that should grow) suddenly shrinks instead of maintaining its size. This creates a jarring visual effect where the cell that should be getting bigger actually gets smaller.

## Root Cause Analysis
The issue occurs when the absorbing cell incorrectly gets the `isCellMerging` flag set in its organic state. When this flag is true along with `isAbsorbing`, the cell applies a shrinking effect that should only happen to cells being absorbed, not the absorbing cell.

### Key States:
- `isAbsorbing`: Cell is in absorption animation
- `isCellMerging`: Special flag for cell-to-cell merging (triggers shrinking)
- `absorbProgress`: Progress of absorption (0-1)

### Correct Behavior:
- **Absorbed cell**: Should have `isAbsorbing=true` and `isCellMerging=true` (shrinks)
- **Absorbing cell**: Should NEVER have `isCellMerging=true` (doesn't shrink)

## Debug Changes Added

### 1. Enhanced Logging in `drawPlayers.ts`
- Added comprehensive debug logging when cells have absorption states
- Logs cell ID, name, mass, and all relevant state flags
- Tracks which cells are absorbing others vs being absorbed

### 2. Preventive Logic in `drawPlayers.ts`
- Added safety checks to clear absorption states from absorbing cells
- Creates safe copies of organic states to prevent shrinking
- Clears orphaned absorption states from cells not involved in merging

### 3. Enhanced Safeguards in `Renderer.tsx`
- Added logging when clearing absorption states from absorbing cells
- Added double-check before starting absorption animations
- Warns when absorbing cells incorrectly have absorption states

### 4. Debugging in `drawOrganicShape.ts`
- Added logging when `startAbsorption` is called
- Added logging when absorption animations complete
- Added logging when shrinkage effects are applied

## Debug Console Output
When the issue occurs, you'll see console logs like:
```
[DEBUG] Player ABC123 (PlayerName): {
  isCellMerging: true,
  isAbsorbing: true,
  absorbProgress: 0.3,
  isAbsorbingOthers: true,  // This should prevent shrinking!
  isBeingAbsorbed: false,
  absorbingInto: undefined,
  mass: 150
}
[DEBUG] Clearing shrinking state from absorbing cell ABC123
```

## How to Test
1. Open browser console
2. Play the game and perform cell merges
3. Watch for debug messages when merging occurs
4. Look for warnings about absorbing cells having incorrect states

## Expected Fix
With these changes:
1. Absorbing cells will never shrink (immediate fix)
2. Debug logs will help identify when/why the wrong states are set
3. Preventive logic will clear incorrect states before they cause issues
4. Multiple safety nets ensure the issue can't occur

## Files Modified
- `src/components/Game/rendering/drawPlayers.ts` - Main fix and debugging
- `src/components/Game/Renderer.tsx` - Additional safeguards
- `src/components/Game/rendering/drawOrganicShape.ts` - Debug logging

## Next Steps
1. Test the game with these changes
2. Monitor console logs during merging
3. If the issue persists, the logs will show exactly when/where wrong states are set
4. Once root cause is identified, remove debug logs and keep the fixes