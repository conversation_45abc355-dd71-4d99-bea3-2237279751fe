import {
  <PERSON><PERSON>hart,
  Area,
  ResponsiveContainer,
  Tooltip,
} from 'recharts';
import { GameStats } from '@/lib/types';

type CompactSizeGraphProps = {
  gameStats: GameStats;
  className?: string;
};

export default function CompactSizeGraph({ gameStats, className = '' }: CompactSizeGraphProps) {
  // Transform the size history data for the chart
  const chartData = gameStats.sizeHistory?.map((point) => ({
    time: point.timestamp / 1000, // Convert to seconds for better readability
    mass: Math.round(point.mass),
    timeFormatted: formatTime(point.timestamp),
  })) || [];

  // Format time for display (MM:SS)
  function formatTime(ms: number) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  // Custom tooltip component
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-neutral-800 border border-neutral-600 rounded-lg p-2 shadow-lg">
          <p className="text-white text-xs">Time: {data.timeFormatted}</p>
          <p className="text-xs" style={{ color: gameStats.playerColor || '#3B82F6' }}>
            Mass: <span className="font-bold">{data.mass}</span>
          </p>
        </div>
      );
    }
    return null;
  };

  if (!chartData.length) {
    return (
      <div className={`flex items-center justify-center h-32 bg-neutral-800 rounded-lg border border-neutral-600 ${className}`}>
        <div className="text-center">
          <div className="text-white/40 text-sm">No size data available</div>
        </div>
      </div>
    );
  }

  // Use player color or fallback to blue
  const playerColor = gameStats.playerColor || '#3B82F6';

  return (
    <div className={`bg-neutral-800 rounded-lg border border-neutral-600 p-3 ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <h4 className="text-sm font-semibold text-white">Size Over Time</h4>
        <div className="text-xs text-white/60">
          Peak: <span className="font-medium" style={{ color: playerColor }}>{Math.round(gameStats.maxMass)}</span>
        </div>
      </div>

      <div className="h-32">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={chartData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
            <defs>
              <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor={playerColor} stopOpacity={0.6}/>
                <stop offset="95%" stopColor={playerColor} stopOpacity={0.1}/>
              </linearGradient>
            </defs>
            
            <Tooltip content={<CustomTooltip />} />
            
            <Area
              type="monotone"
              dataKey="mass"
              stroke={playerColor}
              strokeWidth={2}
              fill="url(#colorGradient)"
              dot={false}
              activeDot={{ r: 3, stroke: playerColor, strokeWidth: 1, fill: playerColor }}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      {/* Simple bottom info */}
      <div className="mt-2 flex items-center justify-between text-xs text-white/60">
        <div>
          Duration: <span className="text-white">{formatTime(gameStats.timeAlive)}</span>
        </div>
        <div>
          Final: <span className="text-white">{Math.round(gameStats.finalMass)}</span>
        </div>
      </div>
    </div>
  );
}