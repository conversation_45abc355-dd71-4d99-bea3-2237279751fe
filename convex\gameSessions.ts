import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Create a new game session
export const createGameSession = mutation({
  args: {
    publicAddress: v.string(),
    playerName: v.string(),
    gameId: v.string(),
    joinCode: v.optional(v.string()),
    wagerStart: v.number(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Find user by public address
    const user = await ctx.db
      .query("users")
      .withIndex("by_public_address", (q) => q.eq("publicAddress", args.publicAddress))
      .first();

    const gameSession = await ctx.db.insert("gameSessions", {
      publicAddress: args.publicAddress,
      userId: user?._id,
      playerName: args.playerName,
      gameId: args.gameId,
      joinCode: args.joinCode,
      stats: {
        timeAlive: 0,
        maxMass: 20, // Starting mass
        killCount: 0,
        pelletsEaten: 0,
        ejectedMasses: 0,
        splits: 0,
        startTime: now,
        finalMass: 20,
        wagerStart: args.wagerStart,
        wagerEnd: args.wagerStart,
        gameResult: "disconnect" as const, // Default, will be updated
      },
      createdAt: now,
      updatedAt: now,
    });

    return gameSession;
  },
});

// Create a completed game session with final statistics
export const createCompletedGameSession = mutation({
  args: {
    publicAddress: v.string(),
    playerName: v.string(),
    gameId: v.string(),
    joinCode: v.optional(v.string()),
    wagerStart: v.number(),
    stats: v.object({
      timeAlive: v.number(),
      maxMass: v.number(),
      killCount: v.number(),
      pelletsEaten: v.number(),
      ejectedMasses: v.number(),
      splits: v.number(),
      startTime: v.number(),
      endTime: v.optional(v.number()),
      finalMass: v.number(),
      wagerStart: v.number(),
      wagerEnd: v.number(),
      gameResult: v.union(
        v.literal("cashout"),
        v.literal("consumed"),
        v.literal("disconnect")
      ),
      consumedBy: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Find user by public address
    const user = await ctx.db
      .query("users")
      .withIndex("by_public_address", (q) => q.eq("publicAddress", args.publicAddress))
      .first();

    const gameSession = await ctx.db.insert("gameSessions", {
      publicAddress: args.publicAddress,
      userId: user?._id,
      playerName: args.playerName,
      gameId: args.gameId,
      joinCode: args.joinCode,
      stats: args.stats,
      createdAt: now,
      updatedAt: now,
    });

    return gameSession;
  },
});

// Update game session statistics
export const updateGameSession = mutation({
  args: {
    sessionId: v.id("gameSessions"),
    stats: v.object({
      timeAlive: v.optional(v.number()),
      maxMass: v.optional(v.number()),
      killCount: v.optional(v.number()),
      pelletsEaten: v.optional(v.number()),
      ejectedMasses: v.optional(v.number()),
      splits: v.optional(v.number()),
      endTime: v.optional(v.number()),
      finalMass: v.optional(v.number()),
      wagerEnd: v.optional(v.number()),
      gameResult: v.optional(v.union(
        v.literal("cashout"),
        v.literal("consumed"),
        v.literal("disconnect")
      )),
      consumedBy: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Game session not found");
    }

    const updatedStats = {
      ...session.stats,
      ...args.stats,
    };

    await ctx.db.patch(args.sessionId, {
      stats: updatedStats,
      updatedAt: Date.now(),
    });

    return args.sessionId;
  },
});

// Get game sessions for a user
export const getUserGameSessions = query({
  args: {
    publicAddress: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 50;
    
    const sessions = await ctx.db
      .query("gameSessions")
      .withIndex("by_public_address", (q) => q.eq("publicAddress", args.publicAddress))
      .order("desc")
      .take(limit);

    return sessions;
  },
});

// Get a specific game session
export const getGameSession = query({
  args: {
    sessionId: v.id("gameSessions"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.sessionId);
  },
});

// Get game sessions by game ID (for leaderboards)
export const getGameSessionsByGameId = query({
  args: {
    gameId: v.string(),
  },
  handler: async (ctx, args) => {
    const sessions = await ctx.db
      .query("gameSessions")
      .withIndex("by_game_id", (q) => q.eq("gameId", args.gameId))
      .collect();

    return sessions.sort((a, b) => b.stats.maxMass - a.stats.maxMass);
  },
});

// Get user statistics summary
export const getUserStatsSummary = query({
  args: {
    publicAddress: v.string(),
  },
  handler: async (ctx, args) => {
    const sessions = await ctx.db
      .query("gameSessions")
      .withIndex("by_public_address", (q) => q.eq("publicAddress", args.publicAddress))
      .collect();

    if (sessions.length === 0) {
      return {
        totalGames: 0,
        totalTimeAlive: 0,
        totalKills: 0,
        maxMassEver: 20,
        totalCashouts: 0,
        totalWinnings: 0,
        averageTimeAlive: 0,
        winRate: 0,
      };
    }

    const totalGames = sessions.length;
    const totalTimeAlive = sessions.reduce((sum, s) => sum + s.stats.timeAlive, 0);
    const totalKills = sessions.reduce((sum, s) => sum + s.stats.killCount, 0);
    const maxMassEver = Math.max(...sessions.map(s => s.stats.maxMass));
    const cashoutSessions = sessions.filter(s => s.stats.gameResult === "cashout");
    const totalCashouts = cashoutSessions.length;
    const totalWinnings = cashoutSessions.reduce((sum, s) => sum + (s.stats.wagerEnd - s.stats.wagerStart), 0);
    const averageTimeAlive = totalTimeAlive / totalGames;
    const winRate = (totalCashouts / totalGames) * 100;

    return {
      totalGames,
      totalTimeAlive,
      totalKills,
      maxMassEver,
      totalCashouts,
      totalWinnings,
      averageTimeAlive,
      winRate,
    };
  },
});