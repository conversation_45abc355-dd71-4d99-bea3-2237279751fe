import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Mutation to create a new transaction
export const createTransaction = mutation({
  args: {
    publicAddress: v.string(),
    type: v.union(
      v.literal("withdraw"),
      v.literal("join_lobby"),
      v.literal("cashout")
    ),
    amountLamports: v.number(),
    amountSol: v.number(),
    amountUsd: v.optional(v.number()),
    signature: v.optional(v.string()),
    blockchainNetwork: v.optional(v.string()),
    gameId: v.optional(v.string()),
    joinCode: v.optional(v.string()),
    metadata: v.optional(v.object({
      recipientAddress: v.optional(v.string()),
      platformFee: v.optional(v.number()),
      solPriceUsd: v.optional(v.number()),
    })),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Get user ID if user exists
    const user = await ctx.db
      .query("users")
      .withIndex("by_public_address", (q) => q.eq("publicAddress", args.publicAddress))
      .first();

    const transactionId = await ctx.db.insert("transactions", {
      publicAddress: args.publicAddress,
      userId: user?._id,
      type: args.type,
      status: "pending",
      amountLamports: args.amountLamports,
      amountSol: args.amountSol,
      amountUsd: args.amountUsd,
      signature: args.signature,
      blockchainNetwork: args.blockchainNetwork || "devnet",
      gameId: args.gameId,
      joinCode: args.joinCode,
      metadata: args.metadata,
      createdAt: now,
      updatedAt: now,
    });

    return transactionId;
  },
});

// Mutation to update transaction status
export const updateTransactionStatus = mutation({
  args: {
    transactionId: v.id("transactions"),
    status: v.union(
      v.literal("pending"),
      v.literal("confirmed"),
      v.literal("failed")
    ),
    signature: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    const updateData: any = {
      status: args.status,
      updatedAt: now,
    };
    
    if (args.signature) {
      updateData.signature = args.signature;
    }

    await ctx.db.patch(args.transactionId, updateData);
    
    return { success: true };
  },
});

// Mutation to update transaction with signature
export const updateTransactionSignature = mutation({
  args: {
    transactionId: v.id("transactions"),
    signature: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.transactionId, {
      signature: args.signature,
      updatedAt: Date.now(),
    });
    
    return { success: true };
  },
});

// Query to get transactions by user
export const getTransactionsByUser = query({
  args: {
    publicAddress: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;
    
    const transactions = await ctx.db
      .query("transactions")
      .withIndex("by_public_address", (q) => q.eq("publicAddress", args.publicAddress))
      .order("desc")
      .take(limit);
    
    return transactions;
  },
});

// Query to get transactions by type
export const getTransactionsByType = query({
  args: {
    type: v.union(
      v.literal("withdraw"),
      v.literal("join_lobby"),
      v.literal("cashout")
    ),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 100;
    
    const transactions = await ctx.db
      .query("transactions")
      .withIndex("by_type", (q) => q.eq("type", args.type))
      .order("desc")
      .take(limit);
    
    return transactions;
  },
});

// Query to get transaction by signature
export const getTransactionBySignature = query({
  args: {
    signature: v.string(),
  },
  handler: async (ctx, args) => {
    const transaction = await ctx.db
      .query("transactions")
      .withIndex("by_signature", (q) => q.eq("signature", args.signature))
      .first();
    
    return transaction;
  },
});

// Query to get transaction statistics
export const getTransactionStats = query({
  args: {
    publicAddress: v.optional(v.string()),
    timeRange: v.optional(v.union(
      v.literal("24h"),
      v.literal("7d"),
      v.literal("30d"),
      v.literal("all")
    )),
  },
  handler: async (ctx, args) => {
    const timeRange = args.timeRange || "all";
    const now = Date.now();
    
    let startTime = 0;
    switch (timeRange) {
      case "24h":
        startTime = now - (24 * 60 * 60 * 1000);
        break;
      case "7d":
        startTime = now - (7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        startTime = now - (30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = 0;
    }

    let allTransactions;
    
    if (args.publicAddress) {
      allTransactions = await ctx.db
        .query("transactions")
        .withIndex("by_public_address", (q) => q.eq("publicAddress", args.publicAddress!))
        .collect();
    } else {
      allTransactions = await ctx.db.query("transactions").collect();
    }
    
    // Filter by time range
    const transactions = allTransactions.filter(tx => tx.createdAt >= startTime);
    
    // Calculate statistics
    const stats = {
      total: transactions.length,
      byType: {
        withdraw: transactions.filter(tx => tx.type === "withdraw").length,
        join_lobby: transactions.filter(tx => tx.type === "join_lobby").length,
        cashout: transactions.filter(tx => tx.type === "cashout").length,
      },
      byStatus: {
        pending: transactions.filter(tx => tx.status === "pending").length,
        confirmed: transactions.filter(tx => tx.status === "confirmed").length,
        failed: transactions.filter(tx => tx.status === "failed").length,
      },
      totalVolume: {
        lamports: transactions.reduce((sum, tx) => sum + tx.amountLamports, 0),
        sol: transactions.reduce((sum, tx) => sum + tx.amountSol, 0),
        usd: transactions.reduce((sum, tx) => sum + (tx.amountUsd || 0), 0),
      },
    };
    
    return stats;
  },
});

// Query to get recent transactions (for admin/monitoring)
export const getRecentTransactions = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;
    
    const transactions = await ctx.db
      .query("transactions")
      .withIndex("by_created_at")
      .order("desc")
      .take(limit);
    
    return transactions;
  },
});

// Query to get pending transactions
export const getPendingTransactions = query({
  args: {
    publicAddress: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("transactions")
      .withIndex("by_status", (q) => q.eq("status", "pending"));
    
    const transactions = await query.collect();
    
    if (args.publicAddress) {
      return transactions.filter(tx => tx.publicAddress === args.publicAddress);
    }
    
    return transactions;
  },
});