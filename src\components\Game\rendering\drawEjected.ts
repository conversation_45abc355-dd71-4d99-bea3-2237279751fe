import { worldToScreen, lamportsToSol } from "../utils";
import type { DisplayEjected } from "../utils";
import { darkenColor } from "./blobStyles";
import { drawOrganicShape, OrganicShapeState, getAbsorptionPosition } from "./drawOrganicShape";

/**
 * Draw player-ejected masses as organic shapes:
 * - Same base/dark colors (based on the ejecting player's color)
 * - Organic wobble outline like players
 * - No name
 * - Wager label floating above if provided
 * - Visually larger than pellets, smaller than players
 */
export function drawEjected(
  ctx: CanvasRenderingContext2D,
  items: Iterable<DisplayEjected>,
  opts: {
    camX: number;
    camY: number;
    width: number;
    height: number;
    scale: number;
    solPriceUsd: number | null;
    organicStates?: Map<string, OrganicShapeState>;
    showMass?: boolean;
  }
) {
  const { camX, camY, width, height, scale, solPriceUsd, organicStates, showMass } = opts;
  const worldHalfW = width / (2 * scale);
  const worldHalfH = height / (2 * scale);

  for (const ej of items) {
    if (Math.abs(ej.x - camX) > worldHalfW + 30 || Math.abs(ej.y - camY) > worldHalfH + 30) continue;

    // Get organic state for this ejected mass
    const organicState = organicStates?.get(ej.id);
    
    // Calculate position with absorption offset if actively absorbing
    let ejectedX = ej.x;
    let ejectedY = ej.y;
    
    if (organicState?.isAbsorbing && organicState.absorbProgress > 0) {
      const absorbPos = getAbsorptionPosition(organicState);
      ejectedX = organicState.absorbStartX + absorbPos.x;
      ejectedY = organicState.absorbStartY + absorbPos.y;
    }

    const pos = worldToScreen(ejectedX, ejectedY, camX, camY, width, height, scale);

    // Visual radius in world units: ensure bigger than pellets but smaller than players.
    // Pellets: ~max(8, sqrt(m)*3.6)
    // Players: ~>= 32 min world px (via massToRadius + camera)
    // Ejected: choose mid-scale (increased for better visibility)
    const radiusWorld = Math.max(20, Math.sqrt(Math.max(0.0001, ej.mass)) * 12.0);
    let radiusScreen = radiusWorld * scale;

    const baseColor = ej.color ?? "#60a5fa";
    const strokeColor = darkenColor(baseColor, 30);

    // Keep outline width roughly constant in screen space
    const ringW = 6;
    
    // Apply absorption effects
    if (organicState?.isAbsorbing) {
      // Shrink during absorption
      const progress = organicState.absorbProgress;
      radiusScreen *= Math.max(0.3, 1 - progress * 0.7);
    }

    ctx.save();
    ctx.translate(pos.x, pos.y);

    // Draw organic shape at origin with stroke outline
    drawOrganicShape(
      ctx,
      0,
      0,
      radiusScreen,
      baseColor,
      strokeColor,
      ringW,
      organicState,
      radiusScreen
    );

    // Wager label (if provided) - prefer wagerDollars if available, fallback to calculating from lamports
    const usd = ej.wagerDollars ?? (solPriceUsd && ej.wagerLamports ? lamportsToSol(ej.wagerLamports) * solPriceUsd : null);
    const sol = lamportsToSol(ej.wagerLamports);
    const label = usd ? `$${usd.toFixed(2)}` : sol > 0 ? `◎${sol.toFixed(3)}` : "";
    if (label) {
      ctx.fillStyle = "#fde68a";
      ctx.textAlign = "center";
      ctx.textBaseline = "bottom";
      const labelPx = Math.max(10, radiusScreen * 0.45);
      ctx.font = `bold ${labelPx}px Inter, ui-sans-serif, system-ui`;
      ctx.shadowColor = "rgba(245,158,11,0.7)";
      ctx.shadowBlur = 6;
      ctx.fillText(label, 0, -radiusScreen - 4);
      ctx.shadowBlur = 0;
    }

    // Draw mass number above ejected mass if showMass is enabled
    if (showMass) {
      ctx.fillStyle = "#ffffff";
      ctx.strokeStyle = "rgba(0,0,0,0.8)";
      ctx.font = `bold ${Math.max(12, radiusScreen * 0.3)}px Inter, ui-sans-serif, system-ui`;
      ctx.textAlign = "center";
      ctx.textBaseline = "bottom";
      ctx.lineWidth = 2;
      const massText = ej.mass.toFixed(1);
      const yOffset = label ? -radiusScreen - 25 : -radiusScreen - 8; // Offset more if wager label exists
      ctx.strokeText(massText, 0, yOffset);
      ctx.fillText(massText, 0, yOffset);
    }

    ctx.restore();
  }
}