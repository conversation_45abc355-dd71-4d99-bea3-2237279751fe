import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Mutation to log user login
export const logUserLogin = mutation({
  args: {
    publicAddress: v.string(),
    displayName: v.optional(v.string()),
    email: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { publicAddress, displayName, email } = args;
    
    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_public_address", (q) => q.eq("publicAddress", publicAddress))
      .first();

    const now = Date.now();

    if (existingUser) {
      // Update existing user's last login and increment login count
      await ctx.db.patch(existingUser._id, {
        lastLoginAt: now,
        loginCount: existingUser.loginCount + 1,
        // Update optional fields if provided
        ...(displayName && { displayName }),
        ...(email && { email }),
      });
      
      return {
        userId: existingUser._id,
        isNewUser: false,
        loginCount: existingUser.loginCount + 1,
      };
    } else {
      // Create new user
      const userId = await ctx.db.insert("users", {
        publicAddress,
        firstLoginAt: now,
        lastLoginAt: now,
        loginCount: 1,
        ...(displayName && { displayName }),
        ...(email && { email }),
      });
      
      return {
        userId,
        isNewUser: true,
        loginCount: 1,
      };
    }
  },
});

// Query to get user by public address
export const getUserByAddress = query({
  args: {
    publicAddress: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_public_address", (q) => q.eq("publicAddress", args.publicAddress))
      .first();
    
    return user;
  },
});

// Query to get all users (for admin purposes)
export const getAllUsers = query({
  args: {},
  handler: async (ctx) => {
    const users = await ctx.db.query("users").collect();
    return users.sort((a, b) => b.lastLoginAt - a.lastLoginAt); // Sort by most recent login
  },
});

// Query to get user login stats
export const getUserStats = query({
  args: {},
  handler: async (ctx) => {
    const users = await ctx.db.query("users").collect();
    
    const totalUsers = users.length;
    const totalLogins = users.reduce((sum, user) => sum + user.loginCount, 0);
    const activeToday = users.filter(user => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return user.lastLoginAt >= today.getTime();
    }).length;
    
    return {
      totalUsers,
      totalLogins,
      activeToday,
    };
  },
});