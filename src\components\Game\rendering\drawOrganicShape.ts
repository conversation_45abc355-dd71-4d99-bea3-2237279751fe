/**
 * Organic shape rendering system for cells and pellets with wobble and deformation effects
 * Optimized for consistent circular blob appearance across all sizes
 */

export interface OrganicShapeState {
  // Wobble animation
  wobblePhase: number;
  wobbleSpeed: number;
  wobbleIntensity: number;
  
  // Absorption animation
  isAbsorbing: boolean;
  absorbProgress: number;
  absorbStartX: number;
  absorbStartY: number;
  absorbTargetX: number;
  absorbTargetY: number;
  isCellMerging: boolean; // Special flag for cell-to-cell merging
  
  // Deformation (for cells being absorbed into)
  dent: {
    active: boolean;
    angle: number;
    size: number;
    decay: number;
  };
  
  // Ripple effects
  ripples: Array<{
    angle: number;
    size: number;
    progress: number;
    speed: number;
  }>;
}

export function createOrganicState(): OrganicShapeState {
  return {
    wobblePhase: Math.random() * Math.PI * 2,
    wobbleSpeed: 1.0 + Math.random() * 0.6, // 1.0-1.6x speed variation
    wobbleIntensity: 0.8 + Math.random() * 0.4, // 0.8-1.2x intensity variation
    
    isAbsorbing: false,
    absorbProgress: 0,
    absorbStartX: 0,
    absorbStartY: 0,
    absorbTargetX: 0,
    absorbTargetY: 0,
    isCellMerging: false,
    
    dent: {
      active: false,
      angle: 0,
      size: 0,
      decay: 0.85
    },
    
    ripples: []
  };
}

export function updateOrganicState(state: OrganicShapeState, dt: number): void {
  // Update wobble phase
  state.wobblePhase += dt * state.wobbleSpeed * 2.5;
  
  // Update absorption progress
  if (state.isAbsorbing) {
    // Slower absorption for cell merging to create more gradual effect
    const absorptionSpeed = state.isCellMerging ? 1.5 : 3; // Slower for cell merging
    const oldProgress = state.absorbProgress;
    state.absorbProgress += dt * absorptionSpeed;
    
    if (state.absorbProgress >= 1) {
      console.log(`[DEBUG] Absorption animation completed:`, {
        isCellMerging: state.isCellMerging,
        finalProgress: state.absorbProgress,
        oldProgress
      });
      state.isAbsorbing = false;
      state.absorbProgress = 0;
      state.isCellMerging = false;
    }
  }
  
  // Update dent decay
  if (state.dent.active) {
    state.dent.size *= Math.pow(state.dent.decay, dt * 60); // 60fps normalized
    if (state.dent.size < 0.5) {
      state.dent.active = false;
    }
  }
  
  // Update ripples
  for (let i = state.ripples.length - 1; i >= 0; i--) {
    const ripple = state.ripples[i];
    ripple.progress += dt * ripple.speed;
    if (ripple.progress >= 2) {
      state.ripples.splice(i, 1);
    }
  }
}

export function startAbsorption(
  state: OrganicShapeState,
  startX: number,
  startY: number,
  targetX: number,
  targetY: number,
  absorberRadius?: number,
  isCellMerging?: boolean
): void {
  console.log(`[DEBUG] startAbsorption called:`, {
    isCellMerging: isCellMerging || false,
    absorberRadius,
    startPos: { x: startX, y: startY },
    targetPos: { x: targetX, y: targetY }
  });
  
  state.isAbsorbing = true;
  state.absorbProgress = 0;
  state.absorbStartX = startX;
  state.absorbStartY = startY;
  state.isCellMerging = isCellMerging || false;
  
  // Scale down absorption movement for larger absorbing shapes
  if (absorberRadius) {
    const dx = targetX - startX;
    const dy = targetY - startY;
    const distance = Math.sqrt(dx * dx + dy * dy);
    
    if (distance > 0) {
      // Calculate movement scale based on absorber size
      // Larger shapes should have less dramatic absorption movement
      const movementScale = Math.max(0.05, Math.min(1, 40 / absorberRadius));
      const scaledDistance = distance * movementScale;
      
      // Apply scaled movement
      const normalizedDx = dx / distance;
      const normalizedDy = dy / distance;
      
      state.absorbTargetX = startX + normalizedDx * scaledDistance;
      state.absorbTargetY = startY + normalizedDy * scaledDistance;
    } else {
      state.absorbTargetX = targetX;
      state.absorbTargetY = targetY;
    }
  } else {
    // Fallback to original behavior if no radius provided
    state.absorbTargetX = targetX;
    state.absorbTargetY = targetY;
  }
}

export function createDent(
  state: OrganicShapeState,
  angle: number,
  size: number
): void {
  state.dent.active = true;
  state.dent.angle = angle;
  state.dent.size = size;
}

export function addRipple(
  state: OrganicShapeState,
  angle: number,
  size: number,
  speed: number = 4
): void {
  state.ripples.push({
    angle,
    size,
    progress: 0,
    speed
  });
}

/**
 * Calculate optimal segment count for smooth circular appearance
 * Uses perimeter-based calculation for consistent segment density
 */
function calculateSegments(radius: number): number {
  // Safety check: ensure radius is valid
  if (!isFinite(radius) || radius <= 0) {
    return 8; // Minimum safe segment count
  }

  // Target: ~4px per segment for smooth curves, without going extreme on huge radii
  const targetSegmentLength = 4.0;
  const circumference = 2 * Math.PI * radius;
  const idealSegments = circumference / targetSegmentLength;

  // Clamp to reasonable bounds with better scaling
  const minSegments = Math.max(8, Math.ceil(radius * 0.25));
  const maxSegments = Math.min(180, Math.ceil(radius * 1.0));

  const result = Math.max(minSegments, Math.min(maxSegments, Math.round(idealSegments)));
  return Math.max(8, result); // Ensure we always return at least 8 segments
}

/**
 * Calculate wobble intensity that scales appropriately with size
 * Maintains relative blob appearance while preventing distortion
 */
function calculateWobbleIntensity(radius: number, baseIntensity: number): number {
  // Base wobble intensity as percentage of radius, scaled down strongly for big shapes
  const basePercentage = 0.115; // Reduced from 0.03 to 0.015 for less wobble overall
  
  // Two-stage reduction as size grows
  const smoothstep = (a: number, b: number, x: number) => {
    const t = Math.max(0, Math.min(1, (x - a) / (b - a)));
    return t * t * (3 - 2 * t);
  };
  const lerp = (a: number, b: number, t: number) => a + (b - a) * t;

  const t1 = smoothstep(30, 120, radius); // Start scaling down earlier
  const baseScale = lerp(1, 0.1, t1); // More aggressive reduction to 10% by r=120
  const t2 = smoothstep(150, 300, radius); // Earlier second transition
  const scale = lerp(baseScale, 0.02, t2); // Down to 2% beyond r~300

  const percent = basePercentage * scale * Math.max(0.6, Math.min(1.4, baseIntensity));
  const wobble = radius * percent;

  // Extra cap to ensure circular look even at very large radii
  return Math.min(wobble, radius * 0.03); // Reduced from 0.06 to 0.03
}

/**
 * Calculate wobble frequency that creates natural-looking motion
 */
function calculateWobbleFrequency(radius: number): number {
  // Larger shapes should have lower frequency wobbles for a natural, circular look
  const smoothstep = (a: number, b: number, x: number) => {
    const t = Math.max(0, Math.min(1, (x - a) / (b - a)));
    return t * t * (3 - 2 * t);
  };
  const lerp = (a: number, b: number, t: number) => a + (b - a) * t;

  // Base frequency gradually drops from ~2.8 (small) to ~1.2 (large)
  const f = lerp(2.8, 1.2, smoothstep(30, 200, radius));
  return f;
}

/**
 * Calculate effect scaling factor for radius to reduce intensity on larger shapes
 */
function effectScaleForRadius(radius: number): number {
  const smoothstep = (a: number, b: number, x: number) => {
    const t = Math.max(0, Math.min(1, (x - a) / (b - a)));
    return t * t * (3 - 2 * t);
  };
  const lerp = (a: number, b: number, t: number) => a + (b - a) * t;

  const t1 = smoothstep(25, 80, radius); // Start scaling down even earlier
  const base = lerp(1, 0.08, t1); // Much more aggressive scaling to 8%
  const t2 = smoothstep(100, 200, radius); // Earlier and tighter second transition
  return lerp(base, 0.02, t2); // Minimal final scale of 2%
}

export function drawOrganicShape(
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  radius: number,
  fillColor: string,
  strokeColor?: string,
  strokeWidth?: number,
  state?: OrganicShapeState,
  effectRadiusOverride?: number
): void {
  // Check for cell merging absorption and apply shrinkage effect
  let shrinkFactor = 1;
  let adjustedFillColor = fillColor;
  let shouldApplyShrinkage = false;
  
  if (state && state.isAbsorbing && state.isCellMerging) {
     const progress = state.absorbProgress;
     
     console.log(`[DEBUG] drawOrganicShape applying shrinkage:`, {
       radius,
       progress,
       isCellMerging: state.isCellMerging,
       isAbsorbing: state.isAbsorbing
     });
     
     // Create a more dramatic shrinking effect for cell merging
     shrinkFactor = Math.max(0.4, 1 - progress * 0.6); // Shrink to 40% minimum
     shouldApplyShrinkage = true;
    
    // Add slight color shift for cell merging (make it slightly lighter)
    const r = parseInt(fillColor.slice(1, 3), 16);
    const g = parseInt(fillColor.slice(3, 5), 16);
    const b = parseInt(fillColor.slice(5, 7), 16);
    
    const lightnessFactor = 1 + progress * 0.15; // Slightly lighter as it merges
    const adjustedR = Math.min(255, Math.floor(r * lightnessFactor));
    const adjustedG = Math.min(255, Math.floor(g * lightnessFactor));
    const adjustedB = Math.min(255, Math.floor(b * lightnessFactor));
    
    adjustedFillColor = `#${adjustedR.toString(16).padStart(2, '0')}${adjustedG.toString(16).padStart(2, '0')}${adjustedB.toString(16).padStart(2, '0')}`;
  }
  
  // Apply scale transformation if needed
  if (shouldApplyShrinkage) {
    ctx.save();
    ctx.scale(shrinkFactor, shrinkFactor);
  }

  if (!state || radius < 5 || radius <= 0 || !isFinite(radius)) {
    // Fallback to simple circle for very small shapes, invalid radius, or no state
    const safeRadius = Math.max(1, isFinite(radius) ? radius : 5);
    ctx.beginPath();
    ctx.arc(x, y, safeRadius, 0, Math.PI * 2);
    ctx.fillStyle = adjustedFillColor;
    ctx.fill();

    if (strokeColor && strokeWidth && strokeWidth > 0) {
      ctx.strokeStyle = strokeColor;
      ctx.lineWidth = strokeWidth;
      ctx.stroke();
    }

    if (shouldApplyShrinkage) {
      ctx.restore();
    }
    return;
  }

  const segments = calculateSegments(radius);
  const time = Date.now() / 1000;

  // Use on-screen visual radius for effect scaling when provided
  const effectRadius = effectRadiusOverride ?? radius;
  const effectScale = effectScaleForRadius(effectRadius);
  const maxWobbleIntensity = calculateWobbleIntensity(effectRadius, state.wobbleIntensity) * effectScale;
  const wobbleFrequency = calculateWobbleFrequency(effectRadius);
  const secondaryFrequency = wobbleFrequency * 2.2;
  const secondaryScale = 0.25; // secondary wobble is subtle and smooths irregularities

  // Build points with limited, smooth wobble
  const points: Array<{ x: number; y: number }> = [];
  for (let i = 0; i < segments; i++) {
    const theta = (i / segments) * Math.PI * 2;

    // Primary wobble with size-appropriate frequency and intensity
    const wobblePhase = theta * wobbleFrequency + state.wobblePhase + time * state.wobbleSpeed * 0.8;
    let wobble = Math.sin(wobblePhase) * maxWobbleIntensity;

    // Secondary wobble (higher frequency, lower intensity) to keep organic but circular
    wobble += Math.sin(theta * secondaryFrequency + state.wobblePhase * 0.7) * maxWobbleIntensity * secondaryScale;

    // Base radius plus wobble
    let r = radius + wobble;

    // Apply dent (inward deformation), scaled down for big shapes
    if (state.dent.active) {
      const angleDiff = Math.atan2(Math.sin(theta - state.dent.angle), Math.cos(theta - state.dent.angle));
      const dentSpread = Math.PI * 0.3; // Reduced from 0.35 for more localized effect
      if (Math.abs(angleDiff) < dentSpread) {
        const dentFactor = Math.cos((angleDiff / dentSpread) * Math.PI * 0.5); // Smooth cosine falloff
        const scaledDentSize = Math.min(state.dent.size * effectScale, radius * 0.28); // Reduced from 0.25 to 0.18
        r -= scaledDentSize * Math.max(0, dentFactor);
      }
    }

    // Apply ripple effects, scaled down for big shapes
    for (const ripple of state.ripples) {
      const rippleAngleDiff = Math.abs(Math.atan2(Math.sin(theta - ripple.angle), Math.cos(theta - ripple.angle)));
      const rippleSpread = Math.PI * 0.4; // Reduced from 0.55 to make ripples more localized
      if (rippleAngleDiff < rippleSpread) {
        const wavePhase = ripple.progress - (rippleAngleDiff / rippleSpread) * 0.4; // Reduced from 0.55
        if (wavePhase > 0 && wavePhase < 1) {
          const waveIntensity = Math.sin(wavePhase * Math.PI);
          const scaledRippleSize = Math.min(ripple.size * effectScale, radius * 0.06); // Reduced from 0.1 to 0.06
          r += waveIntensity * scaledRippleSize;
        }
      }
    }

    // Minimum radius clamp to prevent extreme indenting
    r = Math.max(r, radius * 0.6);

    const px = x + Math.cos(theta) * r;
    const py = y + Math.sin(theta) * r;

    // Safety check: ensure coordinates are finite
    if (isFinite(px) && isFinite(py)) {
      points.push({ x: px, y: py });
    }
  }

  // Safety check: ensure we have valid points
  if (points.length < 3) {
    // Fallback to simple circle if we don't have enough points
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, Math.PI * 2);
    ctx.fillStyle = adjustedFillColor;
    ctx.fill();

    if (strokeColor && strokeWidth && strokeWidth > 0) {
      ctx.strokeStyle = strokeColor;
      ctx.lineWidth = strokeWidth;
      ctx.stroke();
    }

    if (shouldApplyShrinkage) {
      ctx.restore();
    }
    return;
  }

  // Smooth the polygon into a circular-looking blob using quadratic curves
  const midpoint = (a: { x: number; y: number }, b: { x: number; y: number }) => {
    if (!a || !b || !isFinite(a.x) || !isFinite(a.y) || !isFinite(b.x) || !isFinite(b.y)) {
      return { x: x, y: y }; // Fallback to center if points are invalid
    }
    return { x: (a.x + b.x) / 2, y: (a.y + b.y) / 2 };
  };

  const first = points[0];
  const last = points[points.length - 1];

  if (!first || !last) {
    // Fallback if points are invalid
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, Math.PI * 2);
    ctx.fillStyle = adjustedFillColor;
    ctx.fill();

    if (shouldApplyShrinkage) {
      ctx.restore();
    }
    return;
  }

  const startMid = midpoint(last, first);

  ctx.beginPath();
  ctx.moveTo(startMid.x, startMid.y);
  for (let i = 0; i < points.length; i++) {
    const current = points[i];
    const next = points[(i + 1) % points.length];
    const mid = midpoint(current, next);
    ctx.quadraticCurveTo(current.x, current.y, mid.x, mid.y);
  }
  ctx.closePath();

  // Fill the shape with the appropriate color (adjusted for shrinkage or normal)
  ctx.fillStyle = adjustedFillColor;
  ctx.fill();

  // Add stroke if specified
  if (strokeColor && strokeWidth && strokeWidth > 0) {
    ctx.strokeStyle = strokeColor;
    ctx.lineWidth = strokeWidth;
    ctx.stroke();
  }
  
  // Restore context if shrinkage was applied
  if (shouldApplyShrinkage) {
    ctx.restore();
  }
}

// Helper function to get interpolated position during absorption
export function getAbsorptionPosition(state: OrganicShapeState): { x: number; y: number } {
  if (!state.isAbsorbing) {
    return { x: 0, y: 0 }; // No offset
  }
  
  const t = Math.min(1, state.absorbProgress);
  const easeT = 1 - Math.pow(1 - t, 3); // Ease-out cubic for smooth deceleration
  
  const offsetX = (state.absorbTargetX - state.absorbStartX) * easeT;
  const offsetY = (state.absorbTargetY - state.absorbStartY) * easeT;
  
  return { x: offsetX, y: offsetY };
}

// Helper to check if absorption animation is complete
export function isAbsorptionComplete(state: OrganicShapeState): boolean {
  return state.isAbsorbing && state.absorbProgress >= 1;
}