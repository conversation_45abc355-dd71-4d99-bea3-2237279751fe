{"name": "react-vite-ui", "private": true, "version": "0.0.0", "type": "module", "packageManager": "pnpm@9.12.0", "scripts": {"dev": "vite", "dev:server": "tsx watch server/index.ts", "dev:all": "concurrently -k -n \"server,web\" -c \"magenta,cyan\" \"pnpm:dev:server\" \"pnpm:dev\"", "build": "tsc -b && vite build", "build:server": "esbuild server/game-worker.ts --bundle --platform=node --target=node18 --outfile=server/game-worker.js && esbuild server/index.ts --bundle --platform=node --target=node18 --outfile=server/dist/index.js", "start:server": "node server/dist/index.js", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@msgpack/msgpack": "^3.1.2", "@radix-ui/react-slot": "^1.1.2", "@solana/web3.js": "^1.95.3", "@web3auth/modal": "^7.3.2", "@web3auth/openlogin-adapter": "^7.3.2", "@web3auth/solana-provider": "^9.7.0", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.25.4", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "lucide-react": "^0.484.0", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^3.1.2", "socket.io": "^4.7.5", "socket.io-client": "^4.8.1", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "ws": "^8.13.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.23.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^22.13.0", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/uuid": "^9.0.7", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "esbuild": "^0.25.8", "eslint": "^9.23.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwind-merge": "^3.0.2", "tailwindcss": "^3.4.14", "tsx": "^4.19.1", "typescript": "~5.8.2", "typescript-eslint": "^8.28.0", "vite": "^6.2.3"}, "engines": {"node": ">=22.0.0"}}