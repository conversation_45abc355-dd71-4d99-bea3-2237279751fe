import { HelpCircle, LogIn, Wallet, Play, DollarSign, ArrowRight } from "lucide-react";

export default function HowToPlayPanel() {
  const steps = [
    {
      icon: <LogIn className="size-5 text-blue-400" />,
      title: "1. Log In",
      description: "Click the login button in the top bar to authenticate and generate a new wallet."
    },
    {
      icon: <Wallet className="size-5 text-green-400" />,
      title: "2. Deposit SOL",
      description: "Add SOL to your wallet balance using the deposit button. You'll need funds to place wagers."
    },
    {
      icon: <DollarSign className="size-5 text-amber-400" />,
      title: "3. Join Game",
      description: "Select a wager amount ($1, $3, $5, or $10), set your player name, and click 'JOIN GAME'."
    },
    {
      icon: <Play className="size-5 text-purple-400" />,
      title: "4. Play",
      description: "Control your blob with your mouse. Eat pellets to grow, split with SPAC<PERSON>, eject mass with W."
    },
    {
      icon: <ArrowRight className="size-5 text-orange-400" />,
      title: "5. Cash Out",
      description: "Hold Q to start cashing out when you want to secure your winnings. The longer you hold, the more you cash out."
    },
    {
      icon: <Wallet className="size-5 text-emerald-400" />,
      title: "6. Withdraw",
      description: "After cashing out, use the withdraw button to transfer your winnings back to your external wallet."
    }
  ];

  return (
    <div className="panel-dark w-full max-w-md p-6 md:p-8 rounded-2xl">
      <div className="flex items-center gap-2 mb-6">
        <HelpCircle className="size-5 text-blue-400" />
        <h2 className="text-lg font-semibold text-white">How to Play</h2>
      </div>

      <div className="space-y-4">
        {steps.map((step, index) => (
          <div key={index} className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              {step.icon}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-medium text-white mb-1">
                {step.title}
              </h3>
              <p className="text-xs text-white/70 leading-relaxed">
                {step.description}
              </p>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 p-3 bg-amber-500/10 border border-amber-500/20 rounded-lg">
        <div className="flex items-start gap-2">
          <HelpCircle className="size-4 text-amber-400 mt-0.5 flex-shrink-0" />
          <div className="text-xs text-amber-200">
            <strong>Pro Tip:</strong> The bigger you get, the more you can win when cashing out, but you also become a bigger target for other players!
          </div>
        </div>
      </div>
    </div>
  );
}