import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, Clock, CheckCircle, XCircle, ArrowUpRight, GamepadIcon, Coins } from "lucide-react";

type Props = {
  pubkey: string | null;
  solPriceUsd: number | null;
};

export default function TransactionHistoryPanel({ pubkey }: Props) {
  const [timeRange, setTimeRange] = useState<"24h" | "7d" | "30d" | "all">("7d");
  
  // Query transactions for the current user
  const transactions = useQuery(
    (api as any).transactions.getTransactionsByUser,
    pubkey ? { publicAddress: pubkey, limit: 50 } : "skip"
  );

  // Query transaction stats
  const stats = useQuery(
    (api as any).transactions.getTransactionStats,
    pubkey ? { publicAddress: pubkey, timeRange } : "skip"
  );

  if (!pubkey) {
    return (
      <div className="panel-dark w-full max-w-md p-6 md:p-8 rounded-2xl">
        <h3 className="text-lg font-semibold text-white mb-4">Transaction History</h3>
        <p className="text-white/60 text-sm">Connect your wallet to view transaction history</p>
      </div>
    );
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "withdraw":
        return <ArrowUpRight className="w-4 h-4 text-red-400" />;
      case "join_lobby":
        return <GamepadIcon className="w-4 h-4 text-blue-400" />;
      case "cashout":
        return <Coins className="w-4 h-4 text-yellow-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "confirmed":
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case "failed":
        return <XCircle className="w-4 h-4 text-red-400" />;
      case "pending":
        return <Clock className="w-4 h-4 text-yellow-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "confirmed":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      case "failed":
        return "bg-red-500/20 text-red-400 border-red-500/30";
      case "pending":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatTransactionType = (type: string) => {
    switch (type) {
      case "join_lobby":
        return "Join Game";
      case "cashout":
        return "Cashout";
      case "withdraw":
        return "Withdraw";
      default:
        return type;
    }
  };

  return (
    <div className="panel-dark w-full max-w-2xl p-6 md:p-8 rounded-2xl">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white">Transaction History</h3>
        <div className="flex gap-2">
          {(["24h", "7d", "30d", "all"] as const).map((range) => (
            <Button
              key={range}
              variant={timeRange === range ? "default" : "outline"}
              size="sm"
              onClick={() => setTimeRange(range)}
              className="text-xs"
            >
              {range === "all" ? "All" : range}
            </Button>
          ))}
        </div>
      </div>

      {/* Stats Summary */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-neutral-900 rounded-lg p-3 border border-neutral-700">
            <div className="text-xs text-white/60 mb-1">Total</div>
            <div className="text-lg font-semibold text-white">{stats.total}</div>
          </div>
          <div className="bg-neutral-900 rounded-lg p-3 border border-neutral-700">
            <div className="text-xs text-white/60 mb-1">Volume (SOL)</div>
            <div className="text-lg font-semibold text-white">{stats.totalVolume.sol.toFixed(4)}</div>
          </div>
          <div className="bg-neutral-900 rounded-lg p-3 border border-neutral-700">
            <div className="text-xs text-white/60 mb-1">Confirmed</div>
            <div className="text-lg font-semibold text-green-400">{stats.byStatus.confirmed}</div>
          </div>
          <div className="bg-neutral-900 rounded-lg p-3 border border-neutral-700">
            <div className="text-xs text-white/60 mb-1">Failed</div>
            <div className="text-lg font-semibold text-red-400">{stats.byStatus.failed}</div>
          </div>
        </div>
      )}

      {/* Transaction List */}
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {transactions === undefined ? (
          <div className="text-center py-8">
            <div className="animate-spin w-6 h-6 border-2 border-amber-500 border-t-transparent rounded-full mx-auto mb-2"></div>
            <p className="text-white/60 text-sm">Loading transactions...</p>
          </div>
        ) : transactions.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-white/60 text-sm">No transactions found</p>
          </div>
        ) : (
          transactions.map((tx: any) => (
            <div
              key={tx._id}
              className="bg-neutral-900 rounded-lg p-4 border border-neutral-700 hover:border-neutral-600 transition-colors"
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-3">
                  {getTransactionIcon(tx.type)}
                  <div>
                    <div className="font-medium text-white text-sm">
                      {formatTransactionType(tx.type)}
                    </div>
                    <div className="text-xs text-white/60">
                      {formatDate(tx.createdAt)}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={`text-xs ${getStatusColor(tx.status)}`}>
                    <div className="flex items-center gap-1">
                      {getStatusIcon(tx.status)}
                      {tx.status}
                    </div>
                  </Badge>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="text-right">
                  <div className="font-semibold text-white">
                    {tx.amountSol.toFixed(4)} SOL
                  </div>
                  {tx.amountUsd && (
                    <div className="text-xs text-white/60">
                      ≈ ${tx.amountUsd.toFixed(2)} USD
                    </div>
                  )}
                </div>
                {tx.signature && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const explorerUrl = `https://solscan.io/tx/${tx.signature}?cluster=${tx.blockchainNetwork || 'devnet'}`;
                      window.open(explorerUrl, '_blank');
                    }}
                    className="text-xs border-neutral-600 hover:border-neutral-500"
                  >
                    <ExternalLink className="w-3 h-3 mr-1" />
                    View
                  </Button>
                )}
              </div>

              {/* Additional details for specific transaction types */}
              {tx.type === "withdraw" && tx.metadata?.recipientAddress && (
                <div className="mt-2 pt-2 border-t border-neutral-700">
                  <div className="text-xs text-white/60">
                    To: {tx.metadata.recipientAddress.slice(0, 8)}...{tx.metadata.recipientAddress.slice(-8)}
                  </div>
                </div>
              )}
              
              {tx.type === "join_lobby" && tx.joinCode && (
                <div className="mt-2 pt-2 border-t border-neutral-700">
                  <div className="text-xs text-white/60">
                    Game: {tx.joinCode}
                  </div>
                </div>
              )}

              {tx.type === "cashout" && tx.metadata?.platformFee && (
                <div className="mt-2 pt-2 border-t border-neutral-700">
                  <div className="text-xs text-white/60">
                    Platform Fee: {(tx.metadata.platformFee / 1_000_000_000).toFixed(4)} SOL
                  </div>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
}