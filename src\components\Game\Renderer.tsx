import React, { useEffect, useRef } from "react";
import type { TickMsg, WorldPoint } from "@/lib/types";
import { screenToWorld, DisplayPlayer, DisplayPellet, DisplayEjected, DisplayVirus } from "./utils";
import { CONFIG } from "@/shared/game-config";
import { drawGrid } from "./rendering/drawGrid";
import { drawPellets } from "./rendering/drawPellets";
import { drawPlayers } from "./rendering/drawPlayers";
import { drawHitboxes } from "./rendering/drawHitboxes";
import { drawLeaderboard } from "./rendering/drawLeaderboard";
import { drawEjected } from "./rendering/drawEjected";
import { drawViruses } from "./rendering/drawViruses";
import {
  OrganicShapeState,
  createOrganicState,
  updateOrganicState,
  startAbsorption,
  createDent,
  addRipple,
  isAbsorptionComplete
} from "./rendering/drawOrganicShape";

type Props = {
  tick: TickMsg | null;
  youId: string | null;
  className?: string;
  solPriceUsd?: number | null;
  onWorldMouseMove?: (world: WorldPoint) => void;
  // Admin/debug: draw server hitboxes for players and pellets
  showHitboxes?: boolean;
  // Admin/debug: show mass numbers above entities
  showMass?: boolean;
};

export default function GameRenderer({ tick, youId, className, solPriceUsd, onWorldMouseMove, showHitboxes, showMass }: Props) {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const solPriceUsdRef = useRef<number | null>(null);
  const dprRef = useRef(1);
  const widthRef = useRef(0);
  const heightRef = useRef(0);

  // Targets (authoritative latest tick)
  const targetsRef = useRef<{ players: Map<string, DisplayPlayer>; pellets: Map<string, DisplayPellet>; ejected: Map<string, DisplayEjected>; viruses: Map<string, DisplayVirus> }>(
    { players: new Map(), pellets: new Map(), ejected: new Map(), viruses: new Map() }
  );

  // Displayed state that we animate toward
  const displayRef = useRef<{ players: Map<string, DisplayPlayer>; pellets: Map<string, DisplayPellet>; ejected: Map<string, DisplayEjected>; viruses: Map<string, DisplayVirus> }>(
    { players: new Map(), pellets: new Map(), ejected: new Map(), viruses: new Map() }
  );

  // Organic animation states for all entities
  const organicStatesRef = useRef<{
    players: Map<string, OrganicShapeState>;
    ejected: Map<string, OrganicShapeState>;
    pellets: Map<string, OrganicShapeState>;
    viruses: Map<string, OrganicShapeState>;
  }>({
    players: new Map(),
    ejected: new Map(),
    pellets: new Map(),
    viruses: new Map(),
  });


  // Tick history for interpolation
  const prevTickRef = useRef<TickMsg | null>(null);
  const currTickRef = useRef<TickMsg | null>(null);
  const tickArrivalRef = useRef<{ prevArrival: number | null; currArrival: number | null }>({ prevArrival: null, currArrival: null });
  const serverClientOffsetRef = useRef<number>(0); // server_time - performance.now() at last tick arrival
  const INTERPOLATION_DELAY_MS = 20; // render slightly behind latest tick to cut perceived input latency
  const prevPlayersMapRef = useRef<Map<string, import("@/lib/types").PlayerSnap>>(new Map());
  const currPlayersMapRef = useRef<Map<string, import("@/lib/types").PlayerSnap>>(new Map());
  const prevPelletsMapRef = useRef<Map<string, import("@/lib/types").PelletSnap>>(new Map());
  const currPelletsMapRef = useRef<Map<string, import("@/lib/types").PelletSnap>>(new Map());
  const pelletsKnownRef = useRef(false);

  // Ejected masses (optional each tick)
  const prevEjectedMapRef = useRef<Map<string, import("@/lib/types").EjectedSnap>>(new Map());
  const currEjectedMapRef = useRef<Map<string, import("@/lib/types").EjectedSnap>>(new Map());
  const ejectedKnownRef = useRef(false);

  // Viruses (optional each tick)
  const prevVirusesMapRef = useRef<Map<string, import("@/lib/types").VirusSnap>>(new Map());
  const currVirusesMapRef = useRef<Map<string, import("@/lib/types").VirusSnap>>(new Map());
  const virusesKnownRef = useRef(false);

  const lastPointerRef = useRef<{ sx: number; sy: number; active: boolean }>({ sx: 0, sy: 0, active: false });
  
  // Smooth zoom transition
  const smoothScaleRef = useRef<number>(CONFIG.CAMERA_BASE_ZOOM);
  
  // Smooth camera position transitions
  const smoothCamXRef = useRef<number>(0);
  const smoothCamYRef = useRef<number>(0);
  const smoothCamInitializedRef = useRef<boolean>(false);

  // Update targets when a new authoritative tick arrives
  useEffect(() => {
    if (!tick) return;

    // shift tick history
    prevTickRef.current = currTickRef.current;
    tickArrivalRef.current.prevArrival = tickArrivalRef.current.currArrival;
    currTickRef.current = tick;
    tickArrivalRef.current.currArrival = performance.now();
    // estimate server-client offset (server_time - perf.now()) with smoothing to reduce jitter
    {
      const sampleOffset = tick.t - (tickArrivalRef.current.currArrival ?? performance.now());
      const prevOff = serverClientOffsetRef.current;
      serverClientOffsetRef.current =
        isFinite(prevOff) && prevOff !== 0
          ? prevOff + (sampleOffset - prevOff) * 0.2
          : sampleOffset;
    }
    // remember whether this tick included pellets; used to avoid flicker when server omits pellets
    pelletsKnownRef.current = (tick as any).pellets !== undefined;
    // remember whether ejected were included
    ejectedKnownRef.current = (tick as any).ejected !== undefined;
    // remember whether viruses were included
    virusesKnownRef.current = (tick as any).viruses !== undefined;

    // Precompute lookup maps for interpolation to avoid per-frame allocations
    const prev = prevTickRef.current;
    const curr = currTickRef.current;
    prevPlayersMapRef.current.clear();
    currPlayersMapRef.current.clear();
    prevPelletsMapRef.current.clear();
    currPelletsMapRef.current.clear();
    prevEjectedMapRef.current.clear();
    currEjectedMapRef.current.clear();
    prevVirusesMapRef.current.clear();
    currVirusesMapRef.current.clear();
    if (prev) {
      for (const p of prev.players) prevPlayersMapRef.current.set(p.id, p);
      if ((prev as any).pellets) for (const pel of (prev as any).pellets) prevPelletsMapRef.current.set(pel.id, pel);
      if ((prev as any).ejected) for (const ej of (prev as any).ejected) prevEjectedMapRef.current.set(ej.id, ej);
      if ((prev as any).viruses) for (const virus of (prev as any).viruses) prevVirusesMapRef.current.set(virus.id, virus);
    }
    if (curr) {
      for (const p of curr.players) currPlayersMapRef.current.set(p.id, p);
      if ((curr as any).pellets) for (const pel of (curr as any).pellets) currPelletsMapRef.current.set(pel.id, pel);
      if ((curr as any).ejected) for (const ej of (curr as any).ejected) currEjectedMapRef.current.set(ej.id, ej);
      if ((curr as any).viruses) for (const virus of (curr as any).viruses) currVirusesMapRef.current.set(virus.id, virus);
    }

    // Build authoritative maps from current tick (for things like mouse handling / quick access)
    const tp = new Map<string, DisplayPlayer>();
    for (const p of tick.players) {
      tp.set(p.id, {
        id: p.id,
        name: p.name,
        x: p.x,
        y: p.y,
        mass: p.mass,
        color: (p as any).color,
        wagerLamports: (p as any).wagerLamports,
        wagerDollars: (p as any).wagerDollars,
        cashoutHoldProgress: (p as any).cashoutHoldProgress,
      });
    }
    const tpel = new Map<string, DisplayPellet>();
    if (tick.pellets !== undefined) {
      for (const pel of tick.pellets) {
        tpel.set(pel.id, { id: pel.id, x: pel.x, y: pel.y, mass: pel.mass });
      }
    }
    // Ejected masses (player-generated)
    const tej = new Map<string, DisplayEjected>();
    if ((tick as any).ejected !== undefined) {
      for (const ej of (tick as any).ejected!) {
        tej.set(ej.id, { id: ej.id, x: ej.x, y: ej.y, mass: ej.mass, color: (ej as any).color, wagerLamports: (ej as any).wagerLamports, wagerDollars: (ej as any).wagerDollars });
      }
    }

    // Viruses
    const tvir = new Map<string, DisplayVirus>();
    if ((tick as any).viruses !== undefined) {
      for (const virus of (tick as any).viruses!) {
        tvir.set(virus.id, {
          id: virus.id,
          x: virus.x,
          y: virus.y,
          mass: virus.mass,
          feedCount: virus.feedCount,
          absorptionProgress: virus.absorptionProgress,
          beingAbsorbedBy: virus.beingAbsorbedBy
        });
      }
    }

    targetsRef.current.players = tp;
    if (tick.pellets !== undefined) {
      targetsRef.current.pellets = tpel;
    }
    if ((tick as any).ejected !== undefined) {
      targetsRef.current.ejected = tej;
    }
    if ((tick as any).viruses !== undefined) {
      targetsRef.current.viruses = tvir;
    }

    // Ensure display map has entries for new players, prefer previous tick positions if available
    const prevPlayersArr = prevTickRef.current?.players ?? [];
    for (const [id, targ] of tp.entries()) {
      if (!displayRef.current.players.has(id)) {
        const prev = prevPlayersArr.find((pp) => pp.id === id);
        if (prev) {
          displayRef.current.players.set(id, {
            id: prev.id,
            name: prev.name ?? targ.name,
            x: prev.x,
            y: prev.y,
            mass: prev.mass,
            color: (prev as any).color ?? (targ as any).color,
            wagerLamports: prev.wagerLamports,
            wagerDollars: (prev as any).wagerDollars,
          });
        } else {
          displayRef.current.players.set(id, { ...targ });
        }
      }
    }
    // Remove display players that no longer exist
    for (const id of Array.from(displayRef.current.players.keys())) {
      if (!tp.has(id)) displayRef.current.players.delete(id);
    }

    // Pellets: add missing using previous pellet pos if available, remove stale
    const prevPelletsArr = prevTickRef.current?.pellets ?? [];
    for (const [id, pel] of tpel.entries()) {
      if (!displayRef.current.pellets.has(id)) {
        const prev = prevPelletsArr.find((pp) => pp.id === id);
        if (prev) {
          displayRef.current.pellets.set(id, { id: prev.id, x: prev.x, y: prev.y, mass: prev.mass });
        } else {
          displayRef.current.pellets.set(id, { ...pel });
        }
      }
    }
    if (tick.pellets !== undefined) {
      for (const id of Array.from(displayRef.current.pellets.keys())) {
        if (!tpel.has(id)) displayRef.current.pellets.delete(id);
      }
    }

    // Ejected: add missing using previous ejected pos if available, remove stale
    const prevEjectedArr = (prevTickRef.current as any)?.ejected ?? [];
    for (const [id, ej] of tej.entries()) {
      if (!displayRef.current.ejected.has(id)) {
        const prev = prevEjectedArr.find((pe: any) => pe.id === id);
        if (prev) {
          displayRef.current.ejected.set(id, { id: prev.id, x: prev.x, y: prev.y, mass: prev.mass, color: prev.color, wagerLamports: prev.wagerLamports, wagerDollars: prev.wagerDollars });
        } else {
          displayRef.current.ejected.set(id, { ...ej });
        }
      }
    }
    if ((tick as any).ejected !== undefined) {
      for (const id of Array.from(displayRef.current.ejected.keys())) {
        if (!tej.has(id)) displayRef.current.ejected.delete(id);
      }
    }

    // Viruses: add missing using previous virus pos if available, remove stale
    const prevVirusesArr = (prevTickRef.current as any)?.viruses ?? [];
    for (const [id, virus] of tvir.entries()) {
      if (!displayRef.current.viruses.has(id)) {
        const prev = prevVirusesArr.find((pv: any) => pv.id === id);
        if (prev) {
          displayRef.current.viruses.set(id, { id: prev.id, x: prev.x, y: prev.y, mass: prev.mass, feedCount: prev.feedCount });
        } else {
          displayRef.current.viruses.set(id, { ...virus });
        }
      }
    }
    if ((tick as any).viruses !== undefined) {
      for (const id of Array.from(displayRef.current.viruses.keys())) {
        if (!tvir.has(id)) displayRef.current.viruses.delete(id);
      }
    }

    // Clean up removed entities from organic state maps
    // For players, we need to be careful not to remove organic states of cells that are absorbing others
    // as they might have visual effects (dents, ripples) that should continue
    for (const id of Array.from(organicStatesRef.current.players.keys())) {
      if (!tp.has(id)) {
        const organicState = organicStatesRef.current.players.get(id);
        // Only delete if this cell is not currently absorbing another cell (which would have visual effects)
        // Check if any other cell is being absorbed into this one
        let isAbsorbingOthers = false;
        if (organicState && currTickRef.current) {
          for (const player of currTickRef.current.players) {
            if ((player as any).absorbingInto === id) {
              isAbsorbingOthers = true;
              break;
            }
          }
        }
        
        // Only delete the organic state if this cell is not absorbing others
        // This preserves visual effects on the absorbing cell
        if (!isAbsorbingOthers) {
          organicStatesRef.current.players.delete(id);
        }
      }
    }
    for (const id of Array.from(organicStatesRef.current.ejected.keys())) {
      if (!targetsRef.current.ejected.has(id)) organicStatesRef.current.ejected.delete(id);
    }
    for (const id of Array.from(organicStatesRef.current.pellets.keys())) {
      if (!targetsRef.current.pellets.has(id)) organicStatesRef.current.pellets.delete(id);
    }
    for (const id of Array.from(organicStatesRef.current.viruses.keys())) {
      if (!targetsRef.current.viruses.has(id)) organicStatesRef.current.viruses.delete(id);
    }

    // Ensure all entities have organic states BEFORE collision detection
    const organicStates = organicStatesRef.current;
    
    // Ensure all entities have organic states
    for (const [id] of displayRef.current.players.entries()) {
      if (!organicStates.players.has(id)) {
        organicStates.players.set(id, createOrganicState());
      }
    }
    for (const [id] of displayRef.current.pellets.entries()) {
      if (!organicStates.pellets.has(id)) {
        organicStates.pellets.set(id, createOrganicState());
      }
    }
    for (const [id] of displayRef.current.ejected.entries()) {
      if (!organicStates.ejected.has(id)) {
        organicStates.ejected.set(id, createOrganicState());
      }
    }
    for (const [id] of displayRef.current.viruses.entries()) {
      if (!organicStates.viruses.has(id)) {
        organicStates.viruses.set(id, createOrganicState());
      }
    }

    // Detect collision-based absorption events in real-time
    if (currTickRef.current) {
      const currentPellets = (currTickRef.current as any).pellets || [];
      const currentEjected = Array.from(targetsRef.current.ejected.values());
      const currentPlayers = currTickRef.current.players;
      

      
      // Helper function to calculate size-based scaling factors
      const getSizeScaling = (absorberMass: number, absorbedMass: number) => {
        const absorberRadius = Math.max(CONFIG.PLAYER_MIN_RADIUS, Math.sqrt(absorberMass) * CONFIG.MASS_TO_RADIUS_K);
        const massRatio = Math.min(2, Math.sqrt(absorbedMass / Math.max(1, absorberMass))); // Cap at 2x
        // FIXED: Inverse scaling - larger players get SMALLER effects
        const sizeRatio = Math.max(0.15, Math.min(2, 120 / absorberRadius)); // Increased scaling for smaller radii
        return { massRatio, sizeRatio };
      };
      
      // Check for collisions between players and regular pellets
      for (const pellet of currentPellets) {
        const pelletState = organicStatesRef.current.pellets.get(pellet.id);
        
        // Only check collision if pellet isn't already being absorbed
        if (pelletState && !pelletState.isAbsorbing) {
          // Calculate pellet radius (similar to drawPellets logic)
          const pelletRadius = Math.max(8, Math.sqrt(pellet.mass) * 3.6);
          
          for (const player of currentPlayers) {
            // Calculate player radius using the same logic as the game
            const playerRadius = Math.max(CONFIG.PLAYER_MIN_RADIUS, Math.sqrt(player.mass) * CONFIG.MASS_TO_RADIUS_K);
            
            // Check collision: distance between centers < sum of radii
            const dx = player.x - pellet.x;
            const dy = player.y - pellet.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const collisionDistance = playerRadius + pelletRadius;
            
            if (distance <= collisionDistance) {
              const playerState = organicStatesRef.current.players.get(player.id);
              
              if (playerState) {
                // Get size-based scaling
                const { sizeRatio } = getSizeScaling(player.mass, pellet.mass);
                
                // Start pellet absorption animation
                startAbsorption(
                  pelletState,
                  pellet.x,
                  pellet.y,
                  player.x,
                  player.y,
                  playerRadius
                );
                
                // Create dent in player at collision point (size-scaled)
                const angle = Math.atan2(pellet.y - player.y, pellet.x - player.x);
                const dentSize = Math.min(20, pelletRadius * 0.8 * sizeRatio); // Increased cap for dent size
                createDent(playerState, angle, dentSize);
                
                // Add ripple effect to player (size-scaled)
                const rippleSize = Math.min(5, pelletRadius * 0.25 * sizeRatio); // Reduced from 8 and 0.4
                addRipple(playerState, angle, rippleSize);
                
                // Break out of player loop since this pellet is now being absorbed
                break;
              }
            }
          }
        }
      }
      
      // Check for collisions between players and ejected masses
      for (const ejected of currentEjected) {
        const ejectedState = organicStatesRef.current.ejected.get(ejected.id);
        
        // Only check collision if ejected mass isn't already being absorbed
        if (ejectedState && !ejectedState.isAbsorbing) {
          // Calculate ejected mass radius (similar to drawEjected logic)
          const ejectedRadius = Math.max(20, Math.sqrt(Math.max(0.0001, ejected.mass)) * 12.0);
          
          for (const player of currentPlayers) {
            // Calculate player radius
            const playerRadius = Math.max(CONFIG.PLAYER_MIN_RADIUS, Math.sqrt(player.mass) * CONFIG.MASS_TO_RADIUS_K);
            
            // Check collision: distance between centers < sum of radii
            const dx = player.x - ejected.x;
            const dy = player.y - ejected.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const collisionDistance = playerRadius + ejectedRadius;
            
            if (distance <= collisionDistance) {

              
              const playerState = organicStatesRef.current.players.get(player.id);
              
              if (playerState) {
                // Get size-based scaling (ejected masses are larger, so more dramatic effects)
                const { sizeRatio } = getSizeScaling(player.mass, ejected.mass);
                const ejectedScaling = Math.min(2, ejectedRadius / 25); // Additional scaling for ejected masses
                
                // Start ejected mass absorption animation
                startAbsorption(
                  ejectedState,
                  ejected.x,
                  ejected.y,
                  player.x,
                  player.y,
                  playerRadius
                );
                
                // Create larger dent for ejected masses (more dramatic)
                const angle = Math.atan2(ejected.y - player.y, ejected.x - player.x);
                const dentSize = Math.min(25, ejectedRadius * 0.7 * sizeRatio * ejectedScaling); // Increased cap for dent size
                createDent(playerState, angle, dentSize);
                
                // Add stronger ripple effect for ejected masses
                const rippleSize = Math.min(8, ejectedRadius * 0.3 * sizeRatio * ejectedScaling); // Reduced from 12 and 0.5
                addRipple(playerState, angle, rippleSize);
                
                // Break out of player loop since this ejected mass is now being absorbed
                break;
              }
            }
          }
        }
      }

      // Check for collisions between players and viruses
      const currentViruses = Array.from(targetsRef.current.viruses.values());
      for (const virus of currentViruses) {
        const virusState = organicStatesRef.current.viruses.get(virus.id);

        if (virusState) {
          // Calculate virus radius (similar to drawViruses logic)
          const virusRadius = Math.max(35, Math.sqrt(Math.max(0.0001, virus.mass)) * 8.0);

          for (const player of currentPlayers) {
            // Calculate player radius using the same logic as the game
            const playerRadius = Math.max(CONFIG.PLAYER_MIN_RADIUS, Math.sqrt(player.mass) * CONFIG.MASS_TO_RADIUS_K);

            // Check collision: distance between centers < sum of radii
            const dx = player.x - virus.x;
            const dy = player.y - virus.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const collisionDistance = playerRadius + virusRadius;

            // Both large AND medium players get visual effects - creates perfect deception
            const VIRUS_PASSOVER_MASS_THRESHOLD = CONFIG.VIRUS_SPLIT_MASS_THRESHOLD * 0.7;

            if (distance <= collisionDistance && player.mass >= VIRUS_PASSOVER_MASS_THRESHOLD) {
              // LARGE + MEDIUM PLAYERS: Get visual effects when touching viruses
              const playerState = organicStatesRef.current.players.get(player.id);

              if (playerState) {
                const angle = Math.atan2(virus.y - player.y, virus.x - player.x);
                const playerRadius = Math.max(CONFIG.PLAYER_MIN_RADIUS, Math.sqrt(player.mass) * CONFIG.MASS_TO_RADIUS_K);
                const sizeRatio = Math.min(1.0, virusRadius / playerRadius);

                // Visual effects for both large (dangerous) and medium (safe) players
                const dentSize = Math.min(10, virusRadius * 0.2 * sizeRatio);
                createDent(playerState, angle, dentSize);

                const rippleSize = Math.min(3, virusRadius * 0.1 * sizeRatio);
                addRipple(playerState, angle, rippleSize);

                break;
              }
            }
            // LARGE PLAYERS (≥120 mass): Visual effects + actually get split (dangerous)
            // MEDIUM PLAYERS (84-119 mass): Visual effects but DON'T get split (deception!)
            // SMALL PLAYERS (<84 mass): NO visual effects - can hide underneath
          }
        }
      }

      // Check for cell merging and absorption (same-owner cells)
      for (let i = 0; i < currentPlayers.length; i++) {
        const player1 = currentPlayers[i];
        const player1State = organicStatesRef.current.players.get(player1.id);
        
        if (!player1State) continue;
        
        for (let j = i + 1; j < currentPlayers.length; j++) {
          const player2 = currentPlayers[j];
          
          // Check if cells belong to the same owner (multi-cell players or primary cell merging)
          const player1Base = player1.id.includes('#') ? player1.id.split('#')[0] : player1.id;
          const player2Base = player2.id.includes('#') ? player2.id.split('#')[0] : player2.id;
          if (player1Base !== player2Base) continue;
          
          const player2State = organicStatesRef.current.players.get(player2.id);
          if (!player2State) continue;
          
          // Calculate radii
          const player1Radius = Math.max(CONFIG.PLAYER_MIN_RADIUS, Math.sqrt(player1.mass) * CONFIG.MASS_TO_RADIUS_K);
          const player2Radius = Math.max(CONFIG.PLAYER_MIN_RADIUS, Math.sqrt(player2.mass) * CONFIG.MASS_TO_RADIUS_K);
          
          const dx = player2.x - player1.x;
          const dy = player2.y - player1.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          // Check if absorption is happening based on server state
          const player1Absorbing = (player1 as any).absorbingInto === player2.id;
          const player2Absorbing = (player2 as any).absorbingInto === player1.id;
          
          if (player1Absorbing || player2Absorbing) {
            // One cell is being absorbed into the other
            const absorbedPlayer = player1Absorbing ? player1 : player2;
            const absorberPlayer = player1Absorbing ? player2 : player1;
            const absorbedState = player1Absorbing ? player1State : player2State;
            const absorberState = player1Absorbing ? player2State : player1State;
            const absorberRadius = player1Absorbing ? player2Radius : player1Radius;
            
            // CRITICAL: Always ensure the absorbing cell does NOT have the isCellMerging flag
            // This must happen every frame during absorption, not just when starting
            if (absorberState.isCellMerging || absorberState.isAbsorbing) {
              console.log(`[DEBUG] Renderer: Clearing absorption state from absorbing cell ${absorberPlayer.id}`, {
                wasCellMerging: absorberState.isCellMerging,
                wasAbsorbing: absorberState.isAbsorbing,
                absorbProgress: absorberState.absorbProgress
              });
              absorberState.isCellMerging = false;
              absorberState.isAbsorbing = false;
              absorberState.absorbProgress = 0;
            }
            
            // Start absorption animation if not already started
            if (!absorbedState.isAbsorbing) {
              const originalX = (absorbedPlayer as any).originalX ?? absorbedPlayer.x;
              const originalY = (absorbedPlayer as any).originalY ?? absorbedPlayer.y;
              
              console.log(`[DEBUG] Starting absorption animation:`, {
                absorbedId: absorbedPlayer.id,
                absorberId: absorberPlayer.id,
                absorbedMass: absorbedPlayer.mass,
                absorberMass: absorberPlayer.mass
              });
              
              // Double-check that absorber doesn't have absorption state
              if (absorberState.isAbsorbing || absorberState.isCellMerging) {
                console.warn(`[DEBUG] WARNING: Absorber ${absorberPlayer.id} has absorption state, clearing it`);
                absorberState.isAbsorbing = false;
                absorberState.isCellMerging = false;
                absorberState.absorbProgress = 0;
              }
              
              startAbsorption(
                absorbedState,
                originalX,
                originalY,
                absorberPlayer.x,
                absorberPlayer.y,
                absorberRadius,
                true // isCellMerging flag
              );
              
              // Create dent effect on the absorbing cell
              const angle = Math.atan2(absorbedPlayer.y - absorberPlayer.y, absorbedPlayer.x - absorberPlayer.x);
              const dentSize = Math.min(20, Math.sqrt(absorbedPlayer.mass) * 0.6); // Proportional to absorbed cell size
              createDent(absorberState, angle, dentSize);
              
              // Add ripple effect to absorbing cell
              const rippleSize = Math.min(8, Math.sqrt(absorbedPlayer.mass) * 0.3);
              addRipple(absorberState, angle, rippleSize);
            }
          } else {
            // Check if cells are close enough for merge deformation (but not yet absorbing)
            const mergeDistance = (player1Radius + player2Radius) * 1.1;
            
            if (distance <= mergeDistance && distance > 0) {
              // Calculate deformation based on proximity
              const proximityFactor = Math.max(0, 1 - (distance / mergeDistance));
              const deformationStrength = proximityFactor * 12; // Max deformation of 12
              
              // Create mutual deformation
              const angle1to2 = Math.atan2(player2.y - player1.y, player2.x - player1.x);
              const angle2to1 = Math.atan2(player1.y - player2.y, player1.x - player2.x);
              
              // Apply deformation to both cells
              createDent(player1State, angle1to2, deformationStrength);
              createDent(player2State, angle2to1, deformationStrength);
            }
          }
        }
      }
    }
  }, [tick, youId]);

  // keep price updates out of the RAF effect to avoid tearing/restarts
  useEffect(() => {
    solPriceUsdRef.current = solPriceUsd ?? null;
  }, [solPriceUsd]);

  useEffect(() => {
    const c = canvasRef.current;
    if (!c) return;
    const canvas = c;
    const ctx =
      (canvas.getContext("2d", { alpha: false }) as CanvasRenderingContext2D) ||
      (canvas.getContext("2d") as CanvasRenderingContext2D);

    // Hint the browser to keep this layer isolated to reduce flicker on Chrome
    try {
      canvas.style.contain = "strict";
      canvas.style.backfaceVisibility = "hidden";
      canvas.style.transform = "translateZ(0)";
      canvas.style.willChange = "transform, contents";
    } catch {}
    // Prefer sharp rasterization and avoid extra filtering work
    try {
      (ctx as any).imageSmoothingEnabled = false;
    } catch {}

    let raf = 0;
    let lastTime = performance.now();
    let timeAcc = 0;

    function resize() {
      const dpr = window.devicePixelRatio || 1;
      const width = canvas.clientWidth;
      const height = canvas.clientHeight;
      dprRef.current = dpr;
      widthRef.current = width;
      heightRef.current = height;
      canvas.width = Math.floor(width * dpr);
      canvas.height = Math.floor(height * dpr);
      ctx.setTransform(dpr, 0, 0, dpr, 0, 0);
    }

    resize();
    window.addEventListener("resize", resize);

    function step(now: number) {
      const dt = Math.max(0.001, (now - lastTime) / 1000);
      lastTime = now;
      timeAcc += dt;

      // ensure canvas size vars
      const width = widthRef.current || canvas.clientWidth;
      const height = heightRef.current || canvas.clientHeight;

      // Camera based on smoothed "you" (use display positions so camera is smooth)
      const display = displayRef.current;
      const targets = targetsRef.current;
      const meDisplay = youId ? display.players.get(youId) ?? null : null;
      const tYou = youId ? targets.players.get(youId) ?? null : null;
      
      // Calculate camera position - center on all cells when split
      let camX = meDisplay?.x ?? (tYou?.x ?? 0);
      let camY = meDisplay?.y ?? (tYou?.y ?? 0);

      // Zoom logic: zoom OUT as player mass increases (prefer authoritative mass)
      const meTarget = tYou ?? meDisplay;
      const baseZoom = CONFIG.CAMERA_BASE_ZOOM;
      // mass increases should reduce scale (zoom out). cap the effect so scale stays usable.
      const massZoomFactor = meTarget ? Math.min(CONFIG.CAMERA_MASS_ZOOM_CAP, meTarget.mass / CONFIG.CAMERA_MASS_DIVISOR) : 0;
      let targetScale = baseZoom - massZoomFactor; // larger mass -> smaller scale (zoom out)
      
      // Split detection: check if player has multiple cells (split state)
      // Check both current tick and targets for split cells
      const tickPlayerCells = youId && tick ? tick.players.filter(p => {
        const baseId = p.id.split('#')[0];
        return baseId === youId;
      }) : [];
      
      const targetPlayerCells = youId ? Array.from(targets.players.keys()).filter(id => {
        const baseId = id.split('#')[0];
        return baseId === youId;
      }) : [];
      
      // Use whichever has more cells (more up-to-date)
      const playerCells = tickPlayerCells.length >= targetPlayerCells.length ? tickPlayerCells.map(p => p.id) : targetPlayerCells;
      const isSplit = playerCells.length > 1;
      
      // When split, gradually adjust camera to center on all cells (avoid jolting)
      if (isSplit && youId) {
        let totalX = 0;
        let totalY = 0;
        let validCells = 0;
        
        // Calculate center of all player cells
        for (const cellId of playerCells) {
          const displayCell = display.players.get(cellId);
          const targetCell = targets.players.get(cellId);
          const cell = displayCell ?? targetCell;
          
          if (cell) {
            totalX += cell.x;
            totalY += cell.y;
            validCells++;
          }
        }
        
        if (validCells > 0) {
          const centerX = totalX / validCells;
          const centerY = totalY / validCells;
          
          // Only adjust camera if it's already initialized, otherwise use current position
          if (smoothCamInitializedRef.current) {
            // Gradually move toward the center instead of jumping immediately
            const adjustmentFactor = 0.3; // Lower = less aggressive centering
            camX = camX + (centerX - camX) * adjustmentFactor;
            camY = camY + (centerY - camY) * adjustmentFactor;
          } else {
            // If camera not initialized yet, use the center
            camX = centerX;
            camY = centerY;
          }
        }
      }
      

      
      // Apply split zoom: zoom out further when split to see launch direction
      const splitZoomFactor = isSplit ? 0.4 : 0; // Additional zoom out when split
      targetScale -= splitZoomFactor;
      
      targetScale = Math.max(CONFIG.CAMERA_MIN_SCALE, targetScale); // don't let the scale go too small
      
      // Initialize smooth camera position if not set
      if (!smoothCamInitializedRef.current) {
        smoothCamXRef.current = camX;
        smoothCamYRef.current = camY;
        smoothCamInitializedRef.current = true;
      }
      
      // Smooth camera position transitions
      const camSpeed = 8.0; // Higher = faster camera transitions
      const camFactor = 1 - Math.exp(-camSpeed * dt);
      smoothCamXRef.current += (camX - smoothCamXRef.current) * camFactor;
      smoothCamYRef.current += (camY - smoothCamYRef.current) * camFactor;
      const finalCamX = smoothCamXRef.current;
      const finalCamY = smoothCamYRef.current;
      
      // Initialize smooth scale if not set
      if (smoothScaleRef.current === CONFIG.CAMERA_BASE_ZOOM && targetScale !== CONFIG.CAMERA_BASE_ZOOM) {
        smoothScaleRef.current = targetScale;
      }
      
      // Smooth zoom transition with easing
      const zoomSpeed = 7.0; // Higher = faster transitions
      const zoomFactor = 1 - Math.exp(-zoomSpeed * dt);
      smoothScaleRef.current += (targetScale - smoothScaleRef.current) * zoomFactor;
      const scale = smoothScaleRef.current;
      const worldHalfW = width / (2 * scale);
      const worldHalfH = height / (2 * scale);

      // Continuously emit input for current pointer position so movement follows the mouse even when not moving
      if (onWorldMouseMove && lastPointerRef.current.active) {
        const { sx, sy } = lastPointerRef.current;
        const rw = width;
        const rh = height;
        const world = screenToWorld(sx, sy, finalCamX, finalCamY, rw, rh, scale);
        onWorldMouseMove(world);
      }

      // Clear & background
      ctx.fillStyle = "#0b0b0b";
      ctx.fillRect(0, 0, width, height);
      // Reset expensive state to avoid bleeding/ghosting between frames
      ctx.shadowBlur = 0;
      ctx.shadowColor = "transparent";
      ctx.globalCompositeOperation = "source-over";

      // If we're in lobby (no youId or no tick), clear all game elements and show clean canvas
      if (!youId || !tick) {
        // Clear all display state when returning to lobby
        displayRef.current.players.clear();
        displayRef.current.pellets.clear();
        displayRef.current.ejected.clear();
        
        // Clear all target state
        targetsRef.current.players.clear();
        targetsRef.current.pellets.clear();
        targetsRef.current.ejected.clear();
        
        // Clear all organic states
        organicStatesRef.current.players.clear();
        organicStatesRef.current.ejected.clear();
        organicStatesRef.current.pellets.clear();
        
        // Early return - don't render any game elements
        raf = requestAnimationFrame(step);
        return;
      }

      // Grid
      const wSize = (currTickRef.current?.worldSize ?? tick?.worldSize) ?? CONFIG.WORLD_SIZE;
      drawGrid(ctx, width, height, finalCamX, finalCamY, scale, wSize, 40);

      // Interpolation / smoothing:
      // If we have two ticks (prev and curr) use time-based interpolation.
      // We estimate server time as performance.now() + serverClientOffsetRef.current
      const prevTick = prevTickRef.current;
      const currTick = currTickRef.current;
      const nowPerf = performance.now();
      const offset = serverClientOffsetRef.current ?? 0;
      const nowServer = nowPerf + offset;
      const interpServerTime = nowServer - INTERPOLATION_DELAY_MS;

      // Use prebuilt lookup maps to avoid per-frame allocations
      const prevPlayersMap = prevPlayersMapRef.current;
      const currPlayersMap = currPlayersMapRef.current;
      const prevPelletsMap = prevPelletsMapRef.current;
      const currPelletsMap = currPelletsMapRef.current;
      const prevEjectedMap = prevEjectedMapRef.current;
      const currEjectedMap = currEjectedMapRef.current;

      let haveInterp = false;
      if (prevTick && currTick && currTick.t > prevTick.t) {
        const alphaRaw = (interpServerTime - prevTick.t) / (currTick.t - prevTick.t);
        const alpha = Math.max(0, Math.min(1, alphaRaw));
        const extra = alphaRaw > 1 ? Math.min(0.2, alphaRaw - 1) : 0;

        // interpolate players with slight extrapolation when ahead to reduce perceived lag
        for (const [id, disp] of display.players.entries()) {
          const prevP = prevPlayersMap.get(id) ?? currPlayersMap.get(id);
          const currP = currPlayersMap.get(id) ?? prevPlayersMap.get(id);
          if (!prevP || !currP) continue;
          const dx = currP.x - prevP.x;
          const dy = currP.y - prevP.y;
          const dm = (currP.mass ?? 0) - (prevP.mass ?? 0);
          const t = alpha + extra;
          disp.x = prevP.x + dx * t;
          disp.y = prevP.y + dy * t;
          disp.mass = prevP.mass + dm * t;
          disp.wagerLamports = currP.wagerLamports ?? prevP.wagerLamports;
          (disp as any).wagerDollars = (currP as any).wagerDollars ?? (prevP as any).wagerDollars;
          (disp as any).color = (currP as any).color ?? (prevP as any).color ?? (disp as any).color;
          // For cashoutHoldProgress, prefer current tick value (including 0/undefined) over fallbacks
          if ((currP as any).cashoutHoldProgress !== undefined) {
            (disp as any).cashoutHoldProgress = (currP as any).cashoutHoldProgress;
          } else if ((prevP as any).cashoutHoldProgress !== undefined) {
            (disp as any).cashoutHoldProgress = (prevP as any).cashoutHoldProgress;
          } else {
            (disp as any).cashoutHoldProgress = 0; // Default to 0 when no progress data
          }
        }

        // interpolate pellets with slight extrapolation
        for (const [id, disp] of display.pellets.entries()) {
          const prevPel = prevPelletsMap.get(id) ?? currPelletsMap.get(id);
          const currPel = currPelletsMap.get(id) ?? prevPelletsMap.get(id);
          if (!prevPel || !currPel) continue;
          const dx = currPel.x - prevPel.x;
          const dy = currPel.y - prevPel.y;
          const dm = (currPel.mass ?? 0) - (prevPel.mass ?? 0);
          const t = alpha + extra;
          disp.x = prevPel.x + dx * t;
          disp.y = prevPel.y + dy * t;
          disp.mass = prevPel.mass + dm * t;
        }

        // interpolate ejected with slight extrapolation
        for (const [id, disp] of display.ejected.entries()) {
          const prevEj = prevEjectedMap.get(id) ?? currEjectedMap.get(id);
          const currEj = currEjectedMap.get(id) ?? prevEjectedMap.get(id);
          if (!prevEj || !currEj) continue;
          const dx = currEj.x - prevEj.x;
          const dy = currEj.y - prevEj.y;
          const dm = (currEj.mass ?? 0) - (prevEj.mass ?? 0);
          const t = alpha + extra;
          disp.x = prevEj.x + dx * t;
          disp.y = prevEj.y + dy * t;
          disp.mass = prevEj.mass + dm * t;
          (disp as any).color = (currEj as any).color ?? (prevEj as any).color ?? (disp as any).color;
          (disp as any).wagerLamports = (currEj as any).wagerLamports ?? (prevEj as any).wagerLamports ?? (disp as any).wagerLamports;
        }
        haveInterp = true;
      }

      // Fallback smoothing (if we don't have two ticks to interpolate between)
      if (!haveInterp) {
        const k = 8; // responsiveness
        const factor = 1 - Math.exp(-k * dt);
        for (const [id, targetPel] of targets.pellets.entries()) {
          const cur = display.pellets.get(id);
          if (!cur) {
            display.pellets.set(id, { ...targetPel });
            continue;
          }
          cur.x += (targetPel.x - cur.x) * factor;
          cur.y += (targetPel.y - cur.y) * factor;
        }
        // Smooth ejected toward targets
        for (const [id, targetEj] of targets.ejected.entries()) {
          const curEj = display.ejected.get(id);
          if (!curEj) {
            display.ejected.set(id, { ...targetEj });
            continue;
          }
          curEj.x += (targetEj.x - curEj.x) * factor;
          curEj.y += (targetEj.y - curEj.y) * factor;
          curEj.mass += ((targetEj.mass ?? 0) - (curEj.mass ?? 0)) * factor;
          curEj.color = (targetEj as any).color ?? curEj.color;
          curEj.wagerLamports = (targetEj as any).wagerLamports ?? curEj.wagerLamports;
          curEj.wagerDollars = (targetEj as any).wagerDollars ?? curEj.wagerDollars;
        }
        for (const [id, target] of targets.players.entries()) {
          const cur = display.players.get(id);
          if (!cur) {
            display.players.set(id, { ...target });
            continue;
          }
          cur.x += (target.x - cur.x) * factor;
          cur.y += (target.y - cur.y) * factor;
          cur.mass += (target.mass - cur.mass) * factor;
          (cur as any).color = (target as any).color ?? (cur as any).color;
          cur.wagerLamports = target.wagerLamports;
          cur.wagerDollars = target.wagerDollars;
          // For cashoutHoldProgress, always use target value (including 0/undefined) to ensure immediate updates
          (cur as any).cashoutHoldProgress = (target as any).cashoutHoldProgress ?? 0;
        }

        // Interpolate viruses
        for (const [id, target] of targets.viruses.entries()) {
          const cur = display.viruses.get(id);
          if (!cur) {
            display.viruses.set(id, { ...target });
            continue;
          }
          cur.x += (target.x - cur.x) * factor;
          cur.y += (target.y - cur.y) * factor;
          cur.mass += (target.mass - cur.mass) * factor;
          cur.feedCount = target.feedCount; // Feed count should update immediately
        }

      }

      // Update organic animation states for all entities
      const organicStates = organicStatesRef.current;
      
      // Update all organic states
      for (const [, state] of organicStates.players.entries()) {
        updateOrganicState(state, dt);
      }
      for (const [, state] of organicStates.pellets.entries()) {
        updateOrganicState(state, dt);
      }
      for (const [, state] of organicStates.ejected.entries()) {
        updateOrganicState(state, dt);
      }
      for (const [, state] of organicStates.viruses.entries()) {
        updateOrganicState(state, dt);
      }
      
      // Clean up completed absorption animations and remove absorbed entities
      for (const [id, state] of organicStates.pellets.entries()) {
        if (isAbsorptionComplete(state)) {
          // Remove the absorbed pellet from display
          display.pellets.delete(id);
          organicStates.pellets.delete(id);
        }
      }
      
      // Clean up absorbed ejected masses
      for (const [id, state] of organicStates.ejected.entries()) {
        if (isAbsorptionComplete(state)) {
          // Remove the absorbed ejected mass from display
          display.ejected.delete(id);
          organicStates.ejected.delete(id);
        }
      }

      // Clean up absorbed viruses
      for (const [id, state] of organicStates.viruses.entries()) {
        if (isAbsorptionComplete(state)) {
          // Remove the absorbed virus from display
          display.viruses.delete(id);
          organicStates.viruses.delete(id);
        }
      }

      // Remove pellets/ejected that no longer exist (only when included in the last tick)
      if (pelletsKnownRef.current) {
        for (const id of Array.from(display.pellets.keys())) {
          if (!targets.pellets.has(id)) display.pellets.delete(id);
        }
      }
      if (ejectedKnownRef.current) {
        for (const id of Array.from(display.ejected.keys())) {
          if (!targets.ejected.has(id)) display.ejected.delete(id);
        }
      }
      if (virusesKnownRef.current) {
        for (const id of Array.from(display.viruses.keys())) {
          if (!targets.viruses.has(id)) display.viruses.delete(id);
        }
      }
  
      // Draw pellets
      drawPellets(
        ctx,
        display.pellets.values(),
        finalCamX,
        finalCamY,
        width,
        height,
        scale,
        worldHalfW,
        worldHalfH,
        organicStates.pellets,
        showMass
      );

      // Three-tier rendering system for virus interactions
      const VIRUS_PASSOVER_MASS_THRESHOLD = CONFIG.VIRUS_SPLIT_MASS_THRESHOLD * 0.7; // 70% of split threshold

      const smallPlayers = Array.from(display.players.values()).filter(p => p.mass < VIRUS_PASSOVER_MASS_THRESHOLD);
      const mediumPlayers = Array.from(display.players.values()).filter(p => p.mass >= VIRUS_PASSOVER_MASS_THRESHOLD && p.mass < CONFIG.VIRUS_SPLIT_MASS_THRESHOLD);
      const largePlayers = Array.from(display.players.values()).filter(p => p.mass >= CONFIG.VIRUS_SPLIT_MASS_THRESHOLD);

      // Draw small players first (behind viruses for protection)
      drawPlayers(ctx, smallPlayers, {
        youId,
        camX: finalCamX,
        camY: finalCamY,
        width,
        height,
        scale,
        prevPlayersMap,
        currPlayersMap,
        prevTick,
        currTick,
        solPriceUsd: solPriceUsdRef.current,
        organicStates: organicStates.players,
        showMass,
      });

      // Draw viruses (above small players, below medium/large players)
      drawViruses(
        ctx,
        display.viruses.values(),
        finalCamX,
        finalCamY,
        width,
        height,
        scale,
        worldHalfW,
        worldHalfH,
        organicStates.viruses,
        showMass
      );

      // Draw medium players (above viruses, can pass over them)
      drawPlayers(ctx, mediumPlayers, {
        youId,
        camX: finalCamX,
        camY: finalCamY,
        width,
        height,
        scale,
        prevPlayersMap,
        currPlayersMap,
        prevTick,
        currTick,
        solPriceUsd: solPriceUsdRef.current,
        organicStates: organicStates.players,
        showMass,
      });

      // Draw ejected masses (above viruses and medium players, below large players)
      drawEjected(
        ctx,
        display.ejected.values(),
        {
          camX: finalCamX,
          camY: finalCamY,
          width,
          height,
          scale,
          solPriceUsd: solPriceUsdRef.current,
          organicStates: organicStates.ejected,
          showMass,
        }
      );

      // Draw large players (above everything else, can absorb viruses)
      drawPlayers(ctx, largePlayers, {
        youId,
        camX: finalCamX,
        camY: finalCamY,
        width,
        height,
        scale,
        prevPlayersMap,
        currPlayersMap,
        prevTick,
        currTick,
        solPriceUsd: solPriceUsdRef.current,
        organicStates: organicStates.players,
        showMass,
      });

      // Leaderboard and optional debug hitboxes
      {
        const latestTick = currTickRef.current;
        if (latestTick) {
          try {
            if (showHitboxes) {
              drawHitboxes(ctx, display.players.values(), display.pellets.values(), display.viruses.values(), {
                camX: finalCamX,
                camY: finalCamY,
                width,
                height,
                scale,
                youId,
              });
            }
            drawLeaderboard(ctx, width, youId, latestTick, display.players.values(), 10, solPriceUsdRef.current);
          } catch {
            // ignore overlay errors
          }
        }
      }

      // Cleanup players that no longer exist
      for (const id of Array.from(display.players.keys())) {
        if (!targets.players.has(id)) display.players.delete(id);
      }

      raf = requestAnimationFrame(step);
    }

    raf = requestAnimationFrame(step);

    return () => {
      cancelAnimationFrame(raf);
      window.removeEventListener("resize", resize);
    };
  }, [youId, tick?.worldSize, showHitboxes]);

  const handlePointerMove: React.PointerEventHandler<HTMLCanvasElement> = (e) => {
    if (!tick) return;
    const nativeEvt = e.nativeEvent as any;
    const hasOffset = Number.isFinite(nativeEvt?.offsetX) && Number.isFinite(nativeEvt?.offsetY);
    const rect = hasOffset ? null : e.currentTarget.getBoundingClientRect();
    const sx = hasOffset ? nativeEvt.offsetX : e.clientX - (rect!.left);
    const sy = hasOffset ? nativeEvt.offsetY : e.clientY - (rect!.top);
    // Track last pointer even when stationary so we can keep following it as camera moves
    lastPointerRef.current = { sx, sy, active: true };

    // Use the same smoothed camera and scale the renderer uses for consistency
    const finalCamX = smoothCamXRef.current;
    const finalCamY = smoothCamYRef.current;
    const scale = smoothScaleRef.current;

    const rw = widthRef.current || e.currentTarget.clientWidth;
    const rh = heightRef.current || e.currentTarget.clientHeight;
    const world = screenToWorld(sx, sy, finalCamX, finalCamY, rw, rh, scale);
    onWorldMouseMove?.(world);
  };

  return (
    <canvas
      ref={canvasRef}
      className={className}
      onPointerMove={handlePointerMove}
      onPointerEnter={(e) => {
        const rect = e.currentTarget.getBoundingClientRect();
        lastPointerRef.current = { sx: e.clientX - rect.left, sy: e.clientY - rect.top, active: true };
      }}
      onPointerLeave={() => {
        lastPointerRef.current.active = false;
      }}
    />
  );
}