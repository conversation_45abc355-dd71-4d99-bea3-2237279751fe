import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

export default function UserStatsPanel() {
  const userStats = useQuery(api.users.getUserStats);
  const allUsers = useQuery(api.users.getAllUsers);

  if (!userStats) {
    return (
      <div className="bg-black bg-opacity-50 p-4 rounded-md text-white">
        <h3 className="text-lg font-bold mb-2">User Statistics</h3>
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className="bg-black bg-opacity-50 p-4 rounded-md text-white max-w-md">
      <h3 className="text-lg font-bold mb-4">User Statistics</h3>
      
      <div className="space-y-2 mb-4">
        <div className="flex justify-between">
          <span>Total Users:</span>
          <span className="font-semibold">{userStats.totalUsers}</span>
        </div>
        <div className="flex justify-between">
          <span>Total Logins:</span>
          <span className="font-semibold">{userStats.totalLogins}</span>
        </div>
        <div className="flex justify-between">
          <span>Active Today:</span>
          <span className="font-semibold">{userStats.activeToday}</span>
        </div>
      </div>

      {allUsers && allUsers.length > 0 && (
        <div>
          <h4 className="font-semibold mb-2">Recent Users:</h4>
          <div className="space-y-1 max-h-40 overflow-y-auto">
            {allUsers.slice(0, 5).map((user) => (
              <div key={user._id} className="text-xs bg-gray-800 p-2 rounded">
                <div className="font-mono text-green-400">
                  {user.publicAddress.slice(0, 8)}...{user.publicAddress.slice(-4)}
                </div>
                <div className="text-gray-400">
                  Logins: {user.loginCount} | Last: {new Date(user.lastLoginAt).toLocaleDateString()}
                </div>
                {user.displayName && (
                  <div className="text-blue-400">{user.displayName}</div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}