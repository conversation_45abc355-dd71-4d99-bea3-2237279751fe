import { Web3Auth } from "@web3auth/modal";
import { OpenloginAdapter } from "@web3auth/openlogin-adapter";
import { SolanaWallet } from "@web3auth/solana-provider";
import {
  clusterApiUrl,
  Connection,
  LAMPORTS_PER_SOL,
  PublicKey,
  SystemProgram,
  Transaction,
} from "@solana/web3.js";

export type Web3AuthCtx = {
  web3auth: Web3Auth;
  solanaWallet: SolanaWallet | null;
  connected: boolean;
  pubkey: string | null;
};

let ctx: Web3AuthCtx | null = null;

const clientId = import.meta.env.VITE_WEB3AUTH_CLIENT_ID as string;
const network = (import.meta.env.VITE_SOLANA_NETWORK as string) || "devnet";

function getRpcUrl(): string {
  return network === "mainnet"
    ? "https://api.mainnet-beta.solana.com"
    : clusterApiUrl("devnet");
}

const chainConfig = {
  chainNamespace: "solana",
  chainId: network === "mainnet" ? "0x1" : "0x3", // per Web3Auth convention
  rpcTarget: getRpcUrl(),
  displayName: `Solana ${network}`,
  blockExplorer:
    network === "mainnet"
      ? "https://explorer.solana.com"
      : "https://explorer.solana.com?cluster=devnet",
  ticker: "SOL",
  tickerName: "Solana",
} as const;

export async function initWeb3Auth(): Promise<Web3AuthCtx> {
  if (ctx) return ctx;

  const web3auth = new Web3Auth({
    clientId,
    web3AuthNetwork: network === "mainnet" ? "sapphire_mainnet" : "sapphire_devnet",
    chainConfig: chainConfig as any,
    uiConfig: {
      appName: "Agar Wager MVP",
      mode: "dark",
    },
  });

  const openloginAdapter = new OpenloginAdapter({
    loginSettings: {
      mfaLevel: "none",
    },
    adapterSettings: {
      uxMode: "popup",
    } as any,
  });

  web3auth.configureAdapter(openloginAdapter);

  await web3auth.initModal();

  const solanaWallet = web3auth.provider
    ? new SolanaWallet(web3auth.provider as any)
    : null;

  ctx = {
    web3auth,
    solanaWallet,
    connected: !!web3auth.provider,
    pubkey: null,
  };

  if (solanaWallet) {
    try {
      const accounts = await solanaWallet.requestAccounts();
      ctx.pubkey = accounts?.[0] ?? null;
    } catch {
      // not logged in yet
    }
  }

  return ctx!;
}

export async function login(): Promise<Web3AuthCtx> {
  const current = await initWeb3Auth();
  if (current.connected && current.pubkey) return current;

  await current.web3auth.connect();
  const solanaWallet = current.web3auth.provider
    ? new SolanaWallet(current.web3auth.provider as any)
    : null;

  let pubkey: string | null = null;
  if (solanaWallet) {
    const accounts = await solanaWallet.requestAccounts();
    pubkey = accounts?.[0] ?? null;
  }

  ctx = {
    web3auth: current.web3auth,
    solanaWallet,
    connected: !!solanaWallet,
    pubkey,
  };

  // Log user login to Convex if we have a public key
  if (pubkey) {
    try {
      // Get user info from Web3Auth if available
      const userInfo = await current.web3auth.getUserInfo();
      
      // Import Convex client and log the user
      const { ConvexHttpClient } = await import("convex/browser");
      const { api } = await import("../../convex/_generated/api");
      
      const convexClient = new ConvexHttpClient(import.meta.env.VITE_CONVEX_URL!);
      
      await convexClient.mutation(api.users.logUserLogin, {
        publicAddress: pubkey,
        displayName: userInfo?.name || undefined,
        email: userInfo?.email || undefined,
      });
      
      console.log("User login logged to Convex:", pubkey);
    } catch (error) {
      console.warn("Failed to log user login to Convex:", error);
      // Don't throw error - login should still succeed even if logging fails
    }
  }

  return ctx!;
}

export async function logout(): Promise<void> {
  const current = await initWeb3Auth();
  if (current.web3auth.status === "connected") {
    await current.web3auth.logout();
  }
  ctx = null;
}

export function getCtx(): Web3AuthCtx | null {
  return ctx;
}

export function requireWallet(): { wallet: SolanaWallet; pubkey: string } {
  const c = getCtx();
  if (!c?.solanaWallet) throw new Error("Wallet not connected");
  if (!c.pubkey) throw new Error("No public key available");
  return { wallet: c.solanaWallet, pubkey: c.pubkey };
}

export function solToLamports(sol: number): number {
  return Math.round(sol * LAMPORTS_PER_SOL);
}

export function lamportsToSol(lamports: number): number {
  return lamports / LAMPORTS_PER_SOL;
}

/**
 * Get balance (lamports) for a given address. If address is not provided,
 * uses the currently connected wallet public key.
 */
export async function getBalanceLamports(address?: string): Promise<number> {
  const connection = new Connection(getRpcUrl(), "confirmed");
  const pk = new PublicKey(address ?? requireWallet().pubkey);
  return connection.getBalance(pk);
}

export async function sendLamports(toBase58: string, lamports: number): Promise<string> {
  const { wallet, pubkey } = requireWallet();
  const connection = new Connection(getRpcUrl(), "confirmed");

  const from = new PublicKey(pubkey);
  const to = new PublicKey(toBase58);

  const ix = SystemProgram.transfer({
    fromPubkey: from,
    toPubkey: to,
    lamports,
  });

  const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash();

  const tx = new Transaction({
    blockhash,
    lastValidBlockHeight,
    feePayer: from,
  }).add(ix);

  // Web3Auth SolanaWallet handles signing and sending
  const signature = await wallet.signAndSendTransaction(tx);
  return typeof signature === "string" ? signature : (signature as any)?.signature ?? "";
}