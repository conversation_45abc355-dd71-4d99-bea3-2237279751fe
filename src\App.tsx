import { useEffect, useRef, useState } from "react";
import { apiCashout, apiGetHealth, apiGetPotAddress, apiWagerConfirm, apiWagerInitiate } from "@/lib/api";
import TopBar from "@/components/TopBar";
import GameCanvas from "@/components/GameCanvas";
import LobbyBackground from "@/components/LobbyBackground";
import Join<PERSON>anel from "@/components/JoinPanel";
import WalletPanel from "@/components/WalletPanel";
import HowToPlayPanel from "@/components/HowToPlayPanel";
import UserStatsPanel from "@/components/UserStatsPanel";
import CashoutModal from "@/components/CashoutModal";
import LossModal from "@/components/LossModal";
import ActivityHistoryModal from "@/components/ActivityHistoryModal";
import { useMount } from "@/hooks/useMount";
import useSolPrice from "@/hooks/useSolPrice";
import type { TickMsg, WorldPoint } from "@/lib/types";
import { encode as msgpackEncode, decode as msgpackDecode } from "@msgpack/msgpack";
import { useMutation } from "convex/react";
import { api } from "../convex/_generated/api";

function App() {
  // Convex mutations for transaction tracking
  const createTransaction = useMutation((api as any).transactions.createTransaction);
  const updateTransactionStatus = useMutation((api as any).transactions.updateTransactionStatus);
  const updateTransactionSignature = useMutation((api as any).transactions.updateTransactionSignature);

  // Web3 / Wallet
  const [connecting, setConnecting] = useState(false);
  const [pubkey, setPubkey] = useState<string | null>(null);
  // Debug / Admin toggles
  const [showHitboxes, setShowHitboxes] = useState(false);
  const [showMass, setShowMass] = useState(false);

  // Health / Pot
  const [network, setNetwork] = useState("devnet");
  const [potAddress, setPotAddress] = useState<string | null>(null);
  // Wallet balance
  const [balanceLamports, setBalanceLamports] = useState<number | null>(null);
  const { solPriceUsd, refresh: refreshSolPrice } = useSolPrice();
  const [balanceLoading, setBalanceLoading] = useState(false);

  // Wager
  const [tempName, setTempName] = useState("player");
  const [amountSol, setAmountSol] = useState("0.1");
  const [joinCode, setJoinCode] = useState<string | null>(null);
  const [txSig, setTxSig] = useState<string | null>(null);
  const [confirming, setConfirming] = useState(false);

  // Game / WS
  const socketRef = useRef<WebSocket | null>(null);
  const lastInputSentRef = useRef<number>(0);
  const qHoldingRef = useRef(false);
  const [youId, setYouId] = useState<string | null>(null);
  const [tick, setTick] = useState<TickMsg | null>(null);

  // Cashout modal state
  const [cashoutModal, setCashoutModal] = useState<{
    isOpen: boolean;
    amountLamports: number;
    signature: string;
    gameStats?: any;
  }>({
    isOpen: false,
    amountLamports: 0,
    signature: "",
    gameStats: undefined,
  });

  // Activity History modal state
  const [activityHistoryModal, setActivityHistoryModal] = useState(false);

  // Loss modal state
  const [lossModal, setLossModal] = useState<{
    isOpen: boolean;
    gameStats?: any;
  }>({
    isOpen: false,
    gameStats: undefined,
  });

  useMount(() => {
    (async () => {
      try {
        const h = await apiGetHealth();
        setNetwork(h.network);
      } catch {}
      try {
        const p = await apiGetPotAddress();
        setPotAddress(p.potAddress);
      } catch {}
    })();
  });

  // Balance + price fetchers
  const fetchBalance = async (address?: string) => {
    const addr = address ?? pubkey;
    if (!addr) {
      setBalanceLamports(null);
      return;
    }
    setBalanceLoading(true);
    try {
      const { getBalanceLamports } = await import("@/lib/web3auth");
      const lamports = await getBalanceLamports(addr);
      setBalanceLamports(lamports);
    } catch {
    } finally {
      setBalanceLoading(false);
    }
  };

  // sol price is handled by useSolPrice hook

  useEffect(() => {
    if (pubkey) {
      fetchBalance(pubkey);
    } else {
      setBalanceLamports(null);
    }
  }, [pubkey]);

  // useSolPrice hook fetches initial price and polls automatically

  const handleLogin = async () => {
    try {
      setConnecting(true);
      const { login } = await import("@/lib/web3auth");
      const ctx = await login();
      setPubkey(ctx.pubkey);
      await fetchBalance(ctx.pubkey || undefined);
      await refreshSolPrice();
    } catch (e: any) {
      alert("Login failed: " + (e?.message || String(e)));
    } finally {
      setConnecting(false);
    }
  };

  const handleLogout = async () => {
    try {
      const { logout } = await import("@/lib/web3auth");
      await logout();
      setPubkey(null);
      setBalanceLamports(null);
      setBalanceLoading(false);
      setYouId(null);
      setTick(null);
      setJoinCode(null);
      setTxSig(null);
      try {
        socketRef.current?.close();
      } catch {}
      socketRef.current = null;
    } catch (e) {}
  };

  const handleWagerFlow = async () => {
    const amt = parseFloat(amountSol);
    if (!isFinite(amt) || amt <= 0) return alert("Enter a valid SOL amount");
    let pot = potAddress;

    const { solToLamports, sendLamports, login } = await import("@/lib/web3auth");
    let currentPubkey = pubkey;
    if (!currentPubkey) {
      try {
        setConnecting(true);
        const ctx = await login();
        currentPubkey = ctx.pubkey;
        setPubkey(ctx.pubkey);
      } finally {
        setConnecting(false);
      }
    }
    if (!currentPubkey) return alert("Connect wallet first");

    try {
      if (!pot) {
        const resp = await apiGetPotAddress();
        pot = resp.potAddress;
        setPotAddress(pot);
      }
    } catch {}

    let transactionId: string | null = null;

    try {
      setConfirming(true);

      const lamports = solToLamports(amt);
      const amountDollars = solPriceUsd ? amt * solPriceUsd : undefined;
      const init = await apiWagerInitiate({
        amountLamports: lamports,
        playerPubkey: currentPubkey!,
        amountDollars,
        solPriceUsd: solPriceUsd ?? undefined,
      });
      setJoinCode(init.joinCode);
      if (!pot) pot = init.potAddress;

      // Create transaction record before sending
      try {
        transactionId = await createTransaction({
          publicAddress: currentPubkey!,
          type: "join_lobby",
          amountLamports: lamports,
          amountSol: amt,
          amountUsd: solPriceUsd ? amt * solPriceUsd : undefined,
          blockchainNetwork: network,
          gameId: init.joinCode,
          joinCode: init.joinCode,
          metadata: {
            solPriceUsd: solPriceUsd || undefined,
          },
        });
      } catch (e) {
        console.error("Failed to create transaction record:", e);
      }

      const sig = await sendLamports(pot!, lamports);
      setTxSig(sig);

      // Update transaction with signature
      if (transactionId) {
        try {
          await updateTransactionSignature({
            transactionId: transactionId as any,
            signature: sig,
          });
        } catch (e) {
          console.error("Failed to update transaction signature:", e);
        }
      }

      // Ask the server to confirm the transfer.
      // Server performs its own polling/indexing; a single request is sufficient.
      const conf = await apiWagerConfirm({ joinCode: init.joinCode, signature: sig });
      if (!conf || conf.status !== "confirmed") {
        throw new Error("Confirm failed on server");
      }

      // Update transaction status to confirmed
      if (transactionId) {
        try {
          await updateTransactionStatus({
            transactionId: transactionId as any,
            status: "confirmed",
          });
        } catch (e) {
          console.error("Failed to update transaction status:", e);
        }
      }

      await connectSocketAndJoin(init.joinCode);
      await fetchBalance();
    } catch (e: any) {
      // Update transaction status to failed if we have a transaction ID
      if (transactionId) {
        try {
          await updateTransactionStatus({
            transactionId: transactionId as any,
            status: "failed",
          });
        } catch (updateError) {
          console.error("Failed to update transaction status to failed:", updateError);
        }
      }
      alert("Wager flow failed: " + (e?.message || String(e)));
    } finally {
      setConfirming(false);
    }
  };

  async function connectSocketAndJoin(code: string) {
    // Avoid reconnecting
    if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) return;

    // Build ws URL matching page protocol
    // When developing with Vite (served on :5173) the frontend is on a different host than the game server.
    // In that case connect to the local server at :8787. Otherwise use the current host.
    const protocol = window.location.protocol === "https:" ? "wss" : "ws";
    let host = window.location.host;
    if (window.location.port === "5173") {
      host = "localhost:8787";
    }
    const url = `${protocol}://${host}/ws`;
    const ws = new WebSocket(url);
    ws.binaryType = "arraybuffer";
    socketRef.current = ws;

    // Wait for open
    await new Promise<void>((resolve, reject) => {
      const to = setTimeout(() => {
        reject(new Error("WS connect timeout"));
      }, 5000);
      ws.onopen = () => {
        clearTimeout(to);
        resolve();
      };
      ws.onerror = (e) => {
        clearTimeout(to);
        reject(e);
      };
    });

    // send join
    ws.send(msgpackEncode({ type: "join", payload: { joinCode: code, tempName } }));

    // await join_ack or error
    await new Promise<void>((resolve) => {
      const onMessage = (ev: MessageEvent) => {
        try {
          const data = msgpackDecode(new Uint8Array(ev.data as ArrayBuffer));
          const { type, payload } = data as any;
          if (type === "join_ack") {
            if (payload?.ok === false) {
              alert("Join failed: " + (payload?.error || "unknown"));
              ws.removeEventListener("message", onMessage);
              resolve();
              return;
            }
            setYouId((payload?.you?.id) ?? null);
            ws.removeEventListener("message", onMessage);
            resolve();
          }
        } catch (e) {
          // ignore
        }
      };
      ws.addEventListener("message", onMessage);
      // fallback: also resolve after a short delay so app continues even if ack missed
      setTimeout(() => resolve(), 2000);
    });

    // unified message handler for ticks and events
    ws.addEventListener("message", (ev) => {
      try {
        const data = msgpackDecode(new Uint8Array(ev.data as ArrayBuffer));
        const { type, payload } = data as any;
        if (type === "tick") {
          setTick(payload as TickMsg);
        } else if (type === "consumed") {
          // If server told us we were consumed, show loss modal with game stats
          setLossModal({
            isOpen: true,
            gameStats: payload?.gameStats,
          });
          // Reset game state
          setYouId(null);
          setTick(null);
          qHoldingRef.current = false;
        } else if (type === "join_ack") {
          // some servers send join_ack as well
          setYouId((payload?.you?.id) ?? youId);
        } else if (type === "cashout_result") {
          if (payload?.ok) {
            // Log cashout transaction
            const amountLamports = payload.playerNetLamports || payload.grossLamports || 0;
            const signature = payload.signature || "";
            
            if (pubkey && amountLamports > 0) {
              try {
                createTransaction({
                  publicAddress: pubkey,
                  type: "cashout",
                  amountLamports: amountLamports,
                  amountSol: amountLamports / 1_000_000_000,
                  amountUsd: solPriceUsd ? (amountLamports / 1_000_000_000) * solPriceUsd : undefined,
                  signature: signature,
                  blockchainNetwork: network,
                  gameId: youId || undefined,
                  metadata: {
                    platformFee: payload.platformFeeLamports || undefined,
                    solPriceUsd: solPriceUsd || undefined,
                  },
                }).then((transactionId) => {
                  // Mark as confirmed since we have the signature
                  if (signature) {
                    updateTransactionStatus({
                      transactionId: transactionId as any,
                      status: "confirmed",
                    }).catch(console.error);
                  }
                }).catch((e) => {
                  console.error("Failed to create cashout transaction record:", e);
                });
              } catch (e) {
                console.error("Failed to log cashout transaction:", e);
              }
            }

            // Show cashout modal with transaction details
            setCashoutModal({
              isOpen: true,
              amountLamports: amountLamports,
              signature: signature,
              gameStats: undefined, // Will be set by cashout_complete message
            });
            try {
              fetchBalance();
            } catch {}
          } else {
            alert(`Cashout failed: ${payload?.error || "unknown error"}`);
          }
          qHoldingRef.current = false;
        } else if (type === "cashout_complete") {
          // Player has been removed from game and should return to lobby
          console.log("[client] Received cashout_complete:", payload);
          if (payload?.returnToLobby) {
            // Update cashout modal with game stats if available
            if (payload?.gameStats) {
              console.log("[client] Setting game stats:", payload.gameStats);
              setCashoutModal(prev => ({
                ...prev,
                gameStats: payload.gameStats
              }));
            } else {
              console.log("[client] No game stats in cashout_complete payload");
            }
            // Reset game state - this will trigger canvas clearing in GameRenderer
            setYouId(null);
            setTick(null);
            qHoldingRef.current = false;
            // Socket will be closed by server, triggering reconnection flow
          }
        }
      } catch (e) {
        // ignore parse errors
      }
    });

    ws.addEventListener("close", () => {
      // clear client state on close - this will trigger canvas clearing in GameRenderer
      socketRef.current = null;
      setYouId(null);
      setTick(null);
      qHoldingRef.current = false;
    });
  }

  const handleCashout = async () => {
    if (!pubkey) return alert("Connect wallet first");
    const amt = prompt("Cashout amount (SOL)?", "0.1");
    if (!amt) return;
    const n = parseFloat(amt);
    if (!isFinite(n) || n <= 0) return alert("Invalid amount");

    try {
      const { solToLamports } = await import("@/lib/web3auth");
      const { signature } = await apiCashout({
        playerPubkey: pubkey,
        amountLamports: solToLamports(n),
      });
      alert("Cashout tx: " + signature);
      await fetchBalance();
    } catch (e: any) {
      alert("Cashout failed: " + (e?.message || String(e)));
    }
  };

  const handleCloseCashoutModal = () => {
    setCashoutModal({
      isOpen: false,
      amountLamports: 0,
      signature: "",
      gameStats: undefined,
    });
    // Clear any remaining game state and ensure we're back in lobby
    // Setting youId and tick to null will trigger canvas clearing in GameRenderer
    setYouId(null);
    setTick(null);
    setJoinCode(null);
    setTxSig(null);
    qHoldingRef.current = false;
    try {
      socketRef.current?.close();
    } catch {}
    socketRef.current = null;
  };

  const INPUT_THROTTLE_MS = 16; // ~60 Hz input updates (lower latency)
  const onWorldMouseMove = (world: WorldPoint) => {
    const ws = socketRef.current;
    if (!ws || ws.readyState !== WebSocket.OPEN) return;
    const now = performance.now();
    if (now - (lastInputSentRef.current || 0) < INPUT_THROTTLE_MS) return;
    lastInputSentRef.current = now;
    try {
      ws.send(msgpackEncode({ type: "input", payload: { tx: world.x, ty: world.y } }));
    } catch {}
  };

  // Admin mass controls: send set_mass messages to server
  const changeMass = (delta: number) => {
    const ws = socketRef.current;
    if (!ws || ws.readyState !== WebSocket.OPEN || !youId) return;
    const me = tick?.players?.find((p) => p.id === youId);
    const curMass = me?.mass ?? 20;
    const newMass = Math.max(5, Math.round(curMass + delta));
    try {
      ws.send(msgpackEncode({ type: "set_mass", payload: { mass: newMass } }));
    } catch {}
  };

  const setMass = (mass: number) => {
    const ws = socketRef.current;
    if (!ws || ws.readyState !== WebSocket.OPEN || !youId) return;
    try {
      ws.send(msgpackEncode({ type: "set_mass", payload: { mass } }));
    } catch {}
  };

  // Admin balance controls: send add_balance messages to server
  const addBalance = async (solAmount: number) => {
    const ws = socketRef.current;
    if (!ws || ws.readyState !== WebSocket.OPEN || !youId) return;
    const { solToLamports } = await import("@/lib/web3auth");
    const lamports = solToLamports(solAmount);
    try {
      ws.send(msgpackEncode({ type: "add_balance", payload: { lamports } }));
    } catch {}
  };
 
  // Helpers to emit split/eject (usable from keyboard and on-screen buttons)
  const sendSplit = () => {
    const ws = socketRef.current;
    if (!ws || ws.readyState !== WebSocket.OPEN) return;
    try {
      ws.send(msgpackEncode({ type: "split", payload: {} }));
    } catch {}
  };
  const sendEject = () => {
    const ws = socketRef.current;
    if (!ws || ws.readyState !== WebSocket.OPEN) return;
    try {
      ws.send(msgpackEncode({ type: "eject", payload: {} }));
    } catch {}
  };

  const sendCashoutStart = () => {
    const ws = socketRef.current;
    if (!ws || ws.readyState !== WebSocket.OPEN) return;
    try {
      ws.send(msgpackEncode({ type: "cashout_start", payload: {} }));
    } catch {}
  };

  const sendCashoutCancel = () => {
    const ws = socketRef.current;
    if (!ws || ws.readyState !== WebSocket.OPEN) return;
    try {
      ws.send(msgpackEncode({ type: "cashout_cancel", payload: {} }));
    } catch {}
  };

  // Keyboard inputs: split (Space) and eject (W)
  // These emit lightweight events to the authoritative server.
  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      const ws = socketRef.current;
      if (!ws || ws.readyState !== WebSocket.OPEN) return;
      // Spacebar => split (support multiple browser key variants)
      if (e.code === "Space" || e.key === " " || (e as any).key === "Spacebar") {
        sendSplit();
        // prevent page scroll
        e.preventDefault();
      }
      // 'w' or 'W' => eject mass
      if (e.key === "w" || e.key === "W") {
        sendEject();
      }
      // 'q' or 'Q' => start cashout hold
      if (e.key === "q" || e.key === "Q") {
        if (!qHoldingRef.current) {
          qHoldingRef.current = true;
          sendCashoutStart();
        }
      }
    };
    const onKeyUp = (e: KeyboardEvent) => {
      // 'q' or 'Q' => cancel cashout hold
      if (e.key === "q" || e.key === "Q") {
        if (qHoldingRef.current) {
          qHoldingRef.current = false;
          const ws = socketRef.current;
          if (ws && ws.readyState === WebSocket.OPEN) {
            sendCashoutCancel();
          }
        }
      }
    };
    window.addEventListener("keydown", onKeyDown);
    window.addEventListener("keyup", onKeyUp);
    return () => {
      window.removeEventListener("keydown", onKeyDown);
      window.removeEventListener("keyup", onKeyUp);
    };
  }, []);

  return (
    <main className="relative min-h-screen w-full text-foreground bg-grid-dark overflow-hidden">
      <div className="absolute inset-0">
        {youId ? (
          <GameCanvas
            tick={tick}
            youId={youId}
            className="h-full w-full block"
            solPriceUsd={solPriceUsd}
            onWorldMouseMove={onWorldMouseMove}
            showHitboxes={showHitboxes}
            showMass={showMass}
          />
        ) : (
          <LobbyBackground />
        )}
      </div>

      {youId && (
        <div className="fixed left-4 top-20 z-50 bg-black bg-opacity-50 p-2 rounded-md text-white flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <span className="font-bold mr-2">Admin Mass</span>
            <button
              className="px-2 py-1 bg-gray-700 rounded"
              onClick={() => changeMass(-10)}
            >
              -10
            </button>
            <button
              className="px-2 py-1 bg-gray-700 rounded"
              onClick={() => changeMass(-1)}
            >
              -1
            </button>
            <button
              className="px-2 py-1 bg-gray-700 rounded"
              onClick={() => changeMass(30)}
            >
              +30
            </button>
            <button
              className="px-2 py-1 bg-gray-700 rounded"
              onClick={() => changeMass(50)}
            >
              +50
            </button>
            <button
              className="px-2 py-1 bg-yellow-600 rounded text-black"
              onClick={() => setMass(20)}
            >
              Reset
            </button>
          </div>
          <div className="flex items-center gap-2">
            <span className="font-bold mr-2">Admin Balance</span>
            <button
              className="px-2 py-1 bg-green-600 rounded text-black"
              onClick={() => addBalance(0.01)}
            >
              +0.01 SOL
            </button>
            <button
              className="px-2 py-1 bg-green-600 rounded text-black"
              onClick={() => addBalance(0.05)}
            >
              +0.05 SOL
            </button>
            <button
              className="px-2 py-1 bg-green-600 rounded text-black"
              onClick={() => addBalance(0.1)}
            >
              +0.1 SOL
            </button>
            <button
              className="px-2 py-1 bg-green-600 rounded text-black"
              onClick={() => addBalance(0.5)}
            >
              +0.5 SOL
            </button>
          </div>
          <div className="flex items-center gap-2">
            <button
              className={`px-2 py-1 rounded ${showHitboxes ? "bg-green-500 text-black" : "bg-gray-700 text-white"}`}
              onClick={() => setShowHitboxes((s) => !s)}
              title="Toggle hitbox overlay"
            >
              {showHitboxes ? "Hitboxes: ON" : "Hitboxes: OFF"}
            </button>
            <button
              className={`px-2 py-1 rounded ${showMass ? "bg-blue-500 text-black" : "bg-gray-700 text-white"}`}
              onClick={() => setShowMass((s) => !s)}
              title="Toggle mass display"
            >
              {showMass ? "Mass: ON" : "Mass: OFF"}
            </button>
          </div>
        </div>
      )}

{youId && (
 <div className="fixed right-4 bottom-4 z-50 flex flex-col gap-2">
   <button
     className="px-4 py-2 rounded bg-purple-500 text-black font-semibold shadow hover:bg-purple-400 active:scale-95 transition"
     onClick={sendSplit}
     title="Split (Space)"
   >
     Split (Space)
   </button>
   <button
     className="px-4 py-2 rounded bg-blue-500 text-black font-semibold shadow hover:bg-blue-400 active:scale-95 transition"
     onClick={sendEject}
     title="Eject (W)"
   >
     Eject (W)
   </button>
 </div>
)}

{!youId && (
  <>
    <TopBar
      network={network}
      pubkey={pubkey}
      connecting={connecting}
      onLogin={handleLogin}
      onLogout={handleLogout}
    />

    <div className="relative z-10 flex items-center justify-center mt-2">
      <h1 className="heading-hero text-white text-6xl md:text-8xl select-none">swarmbet.fun</h1>
    </div>

    <div className="relative z-10 mt-6 flex items-start justify-center px-4 gap-6">
      <div className="flex flex-col gap-6">
        <HowToPlayPanel />
      </div>
      <div className="flex flex-col gap-6">
        <JoinPanel
          tempName={tempName}
          setTempName={setTempName}
          amountSol={amountSol}
          setAmountSol={setAmountSol}
          confirming={confirming}
          onJoin={handleWagerFlow}
          joinCode={joinCode}
          txSig={txSig}
          solPriceUsd={solPriceUsd}
          pubkey={pubkey}
        />
      </div>
      <div className="flex flex-col gap-6">
        <UserStatsPanel />
        <WalletPanel
          pubkey={pubkey}
          balanceLamports={balanceLamports}
          balanceLoading={balanceLoading}
          solPriceUsd={solPriceUsd}
          onRefresh={async () => {
            try {
              const h = await apiGetHealth();
              setNetwork(h.network);
            } catch {}
            await Promise.all([fetchBalance(), refreshSolPrice()]);
          }}
          onWithdraw={handleCashout}
        />
      </div>
    </div>
  </>
)}

     {/* Cashout Modal */}
     <CashoutModal
       isOpen={cashoutModal.isOpen}
       onClose={handleCloseCashoutModal}
       amountLamports={cashoutModal.amountLamports}
       signature={cashoutModal.signature}
       solPriceUsd={solPriceUsd}
       gameStats={cashoutModal.gameStats}
     />

     {/* Loss Modal */}
     <LossModal
       isOpen={lossModal.isOpen}
       onClose={() => setLossModal({ isOpen: false, gameStats: undefined })}
       gameStats={lossModal.gameStats}
     />

     {/* Activity History Modal */}
     <ActivityHistoryModal
       isOpen={activityHistoryModal}
       onClose={() => setActivityHistoryModal(false)}
       publicAddress={pubkey || undefined}
     />
    </main>
  );
}

export default App;
