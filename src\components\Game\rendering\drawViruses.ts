import { worldToScreen } from "../utils";
import type { DisplayVirus } from "../utils";
import { drawOrganicShape, OrganicShapeState, getAbsorptionPosition } from "./drawOrganicShape";

/**
 * Draw viruses as green, spiky, wobbly organic shapes
 * - Green base color with darker green spikes/outline
 * - Organic wobble like other entities but with spiky appearance
 * - Larger than pellets, can be absorbed by large players causing splits
 * - Can be fed with ejected mass to grow and eventually split
 */
export function drawViruses(
  ctx: CanvasRenderingContext2D,
  viruses: Iterable<DisplayVirus>,
  camX: number,
  camY: number,
  width: number,
  height: number,
  scale: number,
  worldHalfW: number,
  worldHalfH: number,
  organicStates?: Map<string, OrganicShapeState>,
  showMass?: boolean
) {
  for (const virus of viruses) {
    if (Math.abs(virus.x - camX) > worldHalfW + 50 || Math.abs(virus.y - camY) > worldHalfH + 50) continue;
    
    // Get organic state for this virus
    const organicState = organicStates?.get(virus.id);
    
    // Calculate position with absorption offset only if actively absorbing
    let virusX = virus.x;
    let virusY = virus.y;
    
    if (organicState?.isAbsorbing) {
      const offset = getAbsorptionPosition(organicState);
      virusX += offset.x;
      virusY += offset.y;
    }

    const pos = worldToScreen(virusX, virusY, camX, camY, width, height, scale);

    // Virus visual radius - larger than pellets, smaller than most players
    // Base size with slight growth based on feed count
    const baseMass = virus.mass + (virus.feedCount * 10); // Grow slightly when fed
    const radiusWorld = Math.max(35, Math.sqrt(Math.max(0.0001, baseMass)) * 8.0);
    let radiusScreen = radiusWorld * scale;

    // Green virus colors - base green with darker spiky outline
    const baseColor = "#22c55e"; // Green-500
    const spikeColor = "#15803d"; // Green-700 for spikes/outline

    // Viruses don't react visually - they stay solid and unchanged
    // Only the player deforms during absorption

    ctx.save();
    ctx.translate(pos.x, pos.y);

    // Draw spiky virus shape - no visual changes during absorption
    drawSpikyVirus(
      ctx,
      0,
      0,
      radiusScreen,
      baseColor,
      spikeColor,
      organicState,
      radiusScreen
    );

    ctx.restore();

    // Show mass if enabled
    if (showMass) {
      ctx.fillStyle = "#ffffff";
      ctx.font = "12px Arial";
      ctx.textAlign = "center";
      ctx.fillText(
        Math.round(virus.mass).toString(),
        pos.x,
        pos.y - radiusScreen - 15
      );
    }

    // Show feed count for debugging/visualization
    if (virus.feedCount > 0) {
      ctx.fillStyle = "#ffff00";
      ctx.font = "10px Arial";
      ctx.textAlign = "center";
      ctx.fillText(
        `Fed: ${virus.feedCount}`,
        pos.x,
        pos.y + radiusScreen + 20
      );
    }
  }
}

/**
 * Draw a spiky virus using the organic shape system with spike modifications
 */
function drawSpikyVirus(
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  radius: number,
  fillColor: string,
  spikeColor: string,
  state?: OrganicShapeState,
  effectRadiusOverride?: number
): void {
  // First draw the base organic shape
  drawOrganicShape(
    ctx,
    x,
    y,
    radius * 0.8, // Make base slightly smaller to accommodate spikes
    fillColor,
    undefined, // No stroke for base
    0,
    state,
    effectRadiusOverride
  );

  // Then draw spikes around the perimeter
  drawVirusSpikes(ctx, x, y, radius, spikeColor, state);
}

/**
 * Draw spikes around the virus perimeter
 */
function drawVirusSpikes(
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  radius: number,
  spikeColor: string,
  state?: OrganicShapeState
): void {
  const spikeCount = Math.max(8, Math.min(24, Math.floor(radius / 8))); // Scale spike count with size
  const spikeLength = radius * 0.3; // Spikes extend 30% of radius outward
  const time = Date.now() / 1000;
  
  ctx.strokeStyle = spikeColor;
  ctx.lineWidth = Math.max(2, radius / 20);
  ctx.lineCap = "round";

  for (let i = 0; i < spikeCount; i++) {
    const angle = (i / spikeCount) * Math.PI * 2;
    
    // Add some wobble to spike angles if state is available
    let wobbleOffset = 0;
    if (state) {
      wobbleOffset = Math.sin(angle * 3 + state.wobblePhase + time * state.wobbleSpeed) * 0.2;
    }
    
    const spikeAngle = angle + wobbleOffset;
    
    // Spike starts at the edge of the base circle
    const startX = x + Math.cos(spikeAngle) * radius * 0.8;
    const startY = y + Math.sin(spikeAngle) * radius * 0.8;
    
    // Spike ends further out
    const endX = x + Math.cos(spikeAngle) * (radius * 0.8 + spikeLength);
    const endY = y + Math.sin(spikeAngle) * (radius * 0.8 + spikeLength);
    
    ctx.beginPath();
    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
    ctx.stroke();
  }
}
