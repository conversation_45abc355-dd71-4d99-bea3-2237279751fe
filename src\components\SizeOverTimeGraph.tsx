import { useState } from 'react';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Brush,
  ReferenceLine,
} from 'recharts';
import { GameStats } from '@/lib/types';

type SizeOverTimeGraphProps = {
  gameStats: GameStats;
  className?: string;
};

export default function SizeOverTimeGraph({ gameStats, className = '' }: SizeOverTimeGraphProps) {
  const [showBrush, setShowBrush] = useState(false);

  // Transform the size history data for the chart
  const chartData = gameStats.sizeHistory?.map((point) => ({
    time: point.timestamp / 1000, // Convert to seconds for better readability
    mass: Math.round(point.mass),
    timeFormatted: formatTime(point.timestamp),
  })) || [];

  // Format time for display (MM:SS)
  function formatTime(ms: number) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  // Use player color or fallback to blue
  const playerColor = gameStats.playerColor || '#3B82F6';

  // Custom tooltip component
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-neutral-800 border border-neutral-600 rounded-lg p-3 shadow-lg">
          <p className="text-white font-medium">Time: {data.timeFormatted}</p>
          <p style={{ color: playerColor }}>
            Mass: <span className="font-bold">{data.mass}</span>
          </p>
        </div>
      );
    }
    return null;
  };

  // Custom tick formatter for X-axis
  const formatXAxisTick = (tickItem: number) => {
    const minutes = Math.floor(tickItem / 60);
    const seconds = Math.floor(tickItem % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (!chartData.length) {
    return (
      <div className={`flex items-center justify-center h-64 bg-neutral-900 rounded-lg border border-neutral-700 ${className}`}>
        <div className="text-center">
          <div className="text-white/40 mb-2">No size data available</div>
          <div className="text-white/60 text-sm">Size tracking was not available for this game</div>
        </div>
      </div>
    );
  }

  const maxMass = Math.max(...chartData.map(d => d.mass));
  const gameEndTime = gameStats.timeAlive / 1000; // Convert to seconds

  return (
    <div className={`bg-neutral-900 rounded-lg border border-neutral-700 p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">Size Over Time</h3>
        <div className="flex items-center gap-4">
          <div className="text-sm text-white/60">
            Peak: <span className="font-medium" style={{ color: playerColor }}>{Math.round(gameStats.maxMass)}</span>
          </div>
          <button
            onClick={() => setShowBrush(!showBrush)}
            className="text-xs px-2 py-1 bg-neutral-700 hover:bg-neutral-600 text-white rounded transition-colors"
          >
            {showBrush ? 'Hide Zoom' : 'Show Zoom'}
          </button>
        </div>
      </div>

      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <defs>
              <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor={playerColor} stopOpacity={0.6}/>
                <stop offset="95%" stopColor={playerColor} stopOpacity={0.1}/>
              </linearGradient>
            </defs>
            
            <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
            <XAxis
              dataKey="time"
              tickFormatter={formatXAxisTick}
              stroke="#9CA3AF"
              fontSize={12}
              tick={false}
            />
            <YAxis
              stroke="#9CA3AF"
              fontSize={12}
              domain={[0, maxMass * 1.1]}
              tick={false}
            />
            <Tooltip content={<CustomTooltip />} />
            
            {/* Reference line for max mass */}
            <ReferenceLine
              y={gameStats.maxMass}
              stroke={playerColor}
              strokeDasharray="5 5"
              label={{ value: "Peak", position: "top", fill: playerColor }}
            />
            
            {/* Reference line for game end */}
            <ReferenceLine
              x={gameEndTime}
              stroke="#EF4444"
              strokeDasharray="3 3"
              label={{ value: "End", position: "top", fill: "#EF4444" }}
            />

            <Area
              type="monotone"
              dataKey="mass"
              stroke={playerColor}
              strokeWidth={2}
              fill="url(#colorGradient)"
              dot={false}
              activeDot={{ r: 5, stroke: playerColor, strokeWidth: 2, fill: playerColor }}
            />

            {/* Zoom brush */}
            {showBrush && (
              <Brush
                dataKey="time"
                height={30}
                stroke={playerColor}
                tickFormatter={formatXAxisTick}
              />
            )}
          </AreaChart>
        </ResponsiveContainer>
      </div>

      {/* Game result indicator */}
      <div className="mt-3 flex items-center justify-between text-sm">
        <div className="flex items-center gap-4">
          <div className="text-white/60">
            Duration: <span className="text-white">{formatTime(gameStats.timeAlive)}</span>
          </div>
          <div className="text-white/60">
            Final: <span className="text-white">{Math.round(gameStats.finalMass)}</span>
          </div>
        </div>
        <div className={`px-2 py-1 rounded text-xs font-medium ${
          gameStats.gameResult === 'cashout' ? 'bg-green-900 text-green-300' :
          gameStats.gameResult === 'consumed' ? 'bg-red-900 text-red-300' :
          'bg-yellow-900 text-yellow-300'
        }`}>
          {gameStats.gameResult === 'cashout' ? 'Cashed Out' :
           gameStats.gameResult === 'consumed' ? 'Consumed' :
           'Disconnected'}
        </div>
      </div>
    </div>
  );
}