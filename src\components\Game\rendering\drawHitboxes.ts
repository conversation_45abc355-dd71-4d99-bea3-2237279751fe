import { worldToScreen } from "../utils";
import type { DisplayPellet, DisplayPlayer, DisplayVirus } from "../utils";
import { massToRadius as sharedMassToRadius } from "@/shared/game-config";

type HitboxOpts = {
  camX: number;
  camY: number;
  width: number;
  height: number;
  scale: number;
  youId: string | null;
};

export function drawHitboxes(
  ctx: CanvasRenderingContext2D,
  players: Iterable<DisplayPlayer>,
  pellets: Iterable<DisplayPellet>,
  viruses: Iterable<DisplayVirus>,
  opts: HitboxOpts
) {
  const { camX, camY, width, height, scale, youId } = opts;

  ctx.save();
  ctx.globalCompositeOperation = "source-over";

  // Pellets: red outlines
  ctx.strokeStyle = "rgba(255,80,60,0.95)";
  ctx.lineWidth = Math.max(1, 1.5 * scale);
  for (const pel of pellets) {
    const ppos = worldToScreen(pel.x, pel.y, camX, camY, width, height, scale);
    // Server treats pellets as points for collision; draw a small fixed screen-space marker
    const rScreen = Math.max(2, 2 * scale);
    ctx.beginPath();
    ctx.arc(ppos.x, ppos.y, rScreen, 0, Math.PI * 2);
    ctx.stroke();
  }

  // Viruses: purple outlines with inner 50% trigger zone
  ctx.strokeStyle = "rgba(147,51,234,0.95)"; // Purple for virus hitbox
  ctx.lineWidth = Math.max(1, 2 * scale);
  for (const virus of viruses) {
    const vpos = worldToScreen(virus.x, virus.y, camX, camY, width, height, scale);
    // Virus radius calculation (same as rendering)
    const virusRadiusWorld = Math.max(35, Math.sqrt(Math.max(0.0001, virus.mass)) * 8.0);
    const virusRadiusScreen = virusRadiusWorld * scale;

    // Draw outer hitbox (full virus collision area)
    ctx.beginPath();
    ctx.arc(vpos.x, vpos.y, virusRadiusScreen, 0, Math.PI * 2);
    ctx.stroke();

    // Draw inner 50% trigger zone (where player must be to trigger split)
    ctx.strokeStyle = "rgba(239,68,68,0.8)"; // Red for trigger zone
    ctx.lineWidth = Math.max(1, 1.5 * scale);
    ctx.setLineDash([5, 5]); // Dashed line for trigger zone
    ctx.beginPath();
    ctx.arc(vpos.x, vpos.y, virusRadiusScreen * 0.5, 0, Math.PI * 2);
    ctx.stroke();
    ctx.setLineDash([]); // Reset line dash

    // Reset to virus outline color for next virus
    ctx.strokeStyle = "rgba(147,51,234,0.95)";
    ctx.lineWidth = Math.max(1, 2 * scale);
  }

  // Players: green outlines, highlight "you" in yellow
  for (const cur of players) {
    const posHit = worldToScreen(cur.x, cur.y, camX, camY, width, height, scale);
    const radiusWorld = sharedMassToRadius(cur.mass);
    const radiusScreen = Math.max(4, radiusWorld * scale);
    ctx.beginPath();
    if (cur.id === youId) {
      ctx.strokeStyle = "rgba(245,195,66,0.95)";
      ctx.lineWidth = Math.max(2, 2.5 * scale);
    } else {
      ctx.strokeStyle = "rgba(60,200,120,0.95)";
      ctx.lineWidth = Math.max(1.5, 1.8 * scale);
    }
    ctx.arc(posHit.x, posHit.y, radiusScreen, 0, Math.PI * 2);
    ctx.stroke();
  }

  ctx.restore();
}