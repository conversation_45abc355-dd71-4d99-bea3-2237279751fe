// Shared types for game state and networking

export type PlayerSnap = {
  id: string;
  name: string;
  x: number;
  y: number;
  mass: number;
  // Optional stable color for this player's cells (hex)
  color?: string;
  // wager in lamports (optional in older ticks)
  wagerLamports?: number;
  // wager in dollars (current amount during gameplay)
  wagerDollars?: number;
  // Optional server-authoritative Q-hold cashout progress for this blob (0..1).
  // When present for your own blobs, render a progress arc around the blob.
  cashoutHoldProgress?: number;
};

export type PelletSnap = {
  id: string;
  x: number;
  y: number;
  mass: number;
};

/**
 * Player-ejected masses (distinct from environmental pellets).
 * Rendered as mini versions of the ejecting player; carries color and an optional wager amount label.
 */
export type EjectedSnap = {
  id: string;
  x: number;
  y: number;
  mass: number;
  // Color of the ejecting player (hex)
  color?: string;
  // Wager amount displayed above the ejected mass (lamports). Purely cosmetic.
  wagerLamports?: number;
  // Wager amount displayed above the ejected mass (dollars). Purely cosmetic.
  wagerDollars?: number;
};

export type VirusSnap = {
  id: string;
  x: number;
  y: number;
  mass: number;
  // Number of times this virus has been fed (for tracking split threshold)
  feedCount: number;
  // Absorption progress (0-1) for gradual consumption
  absorptionProgress?: number;
  // ID of the cell absorbing this virus
  beingAbsorbedBy?: string;
};


export type LeaderboardEntry = {
  id: string;
  name: string;
  mass: number;
  // Optional wager amount for leaderboard display (lamports)
  wagerLamports?: number;
  // Optional wager amount for leaderboard display (dollars)
  wagerDollars?: number;
};

export type TickMsg = {
  t: number;
  players: PlayerSnap[];
  // Pellets may be throttled by the server to reduce bandwidth; absent on some ticks
  pellets?: PelletSnap[];
  // Player-ejected masses (distinct from environmental pellets). May be omitted on some ticks.
  ejected?: EjectedSnap[];
  // Viruses may be throttled by the server to reduce bandwidth; absent on some ticks
  viruses?: VirusSnap[];
  // Optional authoritative leaderboard provided by the server (top N players)
  leaderboard?: LeaderboardEntry[];
  worldSize: number;
};

export type WorldPoint = {
  x: number;
  y: number;
};

// Game statistics for tracking player performance
export type GameStats = {
  timeAlive: number; // in milliseconds
  maxMass: number;
  killCount: number;
  pelletsEaten: number;
  ejectedMasses: number;
  splits: number;
  startTime: number;
  endTime?: number;
  finalMass: number;
  wagerStart: number; // starting wager in lamports
  wagerEnd: number; // ending wager in lamports
  gameResult: 'cashout' | 'consumed' | 'disconnect';
  consumedBy?: string; // player ID who consumed this player
  playerColor?: string; // player's color (hex)
  // Size history for graphs - array of {timestamp, mass} points
  sizeHistory?: Array<{
    timestamp: number; // milliseconds since game start
    mass: number; // total mass at this point
  }>;
};

// Game session data for database storage
export type GameSession = {
  id: string;
  playerId: string;
  playerName: string;
  gameId: string;
  joinCode?: string;
  stats: GameStats;
  createdAt: number;
  updatedAt: number;
};