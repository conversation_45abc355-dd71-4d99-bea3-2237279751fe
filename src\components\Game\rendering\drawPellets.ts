import { worldToScreen } from "../utils";
import { colorForPellet, pelletSizeMultiplier, darkenColor } from "./blobStyles";
import { drawOrganicShape, getAbsorptionPosition, OrganicShapeState } from "./drawOrganicShape";
import type { DisplayPellet } from "../utils";

export function drawPellets(
  ctx: CanvasRenderingContext2D,
  pellets: Iterable<DisplayPellet>,
  camX: number,
  camY: number,
  width: number,
  height: number,
  scale: number,
  worldHalfW: number,
  worldHalfH: number,
  organicStates?: Map<string, OrganicShapeState>,
  showMass?: boolean
) {
  for (const pel of pellets) {
    if (Math.abs(pel.x - camX) > worldHalfW + 20 || Math.abs(pel.y - camY) > worldHalfH + 20) continue;
    
    // Get organic state for this pellet
    const organicState = organicStates?.get(pel.id);
    
    // Calculate position with absorption offset only if actively absorbing
    let pelletX = pel.x;
    let pelletY = pel.y;
    
    if (organicState?.isAbsorbing && organicState.absorbProgress > 0) {
      const absorbPos = getAbsorptionPosition(organicState);
      pelletX = organicState.absorbStartX + absorbPos.x;
      pelletY = organicState.absorbStartY + absorbPos.y;
    }
    
    const ppos = worldToScreen(pelletX, pelletY, camX, camY, width, height, scale);

    // Bigger base size and a small per-pellet deterministic variation for visual variety.
    const basePrWorld = Math.max(8, Math.sqrt(pel.mass) * 3.6) * pelletSizeMultiplier(pel.id);
    let pr = basePrWorld * scale;

    // Get color for this pellet
    const baseColor = colorForPellet(pel.id);
    const strokeColor = darkenColor(baseColor, 20);

    // Modify size for absorption effect, but keep original colors
    let finalBaseColor = baseColor;
    let finalStrokeColor = strokeColor;
    
    if (organicState?.isAbsorbing) {
      // Only shrink during absorption, don't change colors
      const progress = organicState.absorbProgress;
      pr *= Math.max(0.3, 1 - progress * 0.7);
    }

    // Draw organic pellet with wobble and effects
    drawOrganicShape(
      ctx,
      ppos.x,
      ppos.y,
      pr,
      finalBaseColor,
      finalStrokeColor,
      Math.max(1, pr * 0.08), // Subtle stroke width
      organicState,
      pr
    );
    


    // Draw mass number above pellet if showMass is enabled
    if (showMass) {
      ctx.save();
      ctx.fillStyle = "#ffffff";
      ctx.strokeStyle = "rgba(0,0,0,0.8)";
      ctx.font = `bold ${Math.max(10, pr * 0.4)}px Inter, ui-sans-serif, system-ui`;
      ctx.textAlign = "center";
      ctx.textBaseline = "bottom";
      ctx.lineWidth = 2;
      const massText = pel.mass.toFixed(1);
      ctx.strokeText(massText, ppos.x, ppos.y - pr - 5);
      ctx.fillText(massText, ppos.x, ppos.y - pr - 5);
      ctx.restore();
    }
  }
}