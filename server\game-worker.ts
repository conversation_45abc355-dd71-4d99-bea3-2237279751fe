import { parentPort } from "worker_threads";
import { CONFIG, massToRadius as sharedMassToRadius, speedForMass } from "../src/shared/game-config.ts";

/**
 * Authoritative simulation running in a Worker thread.
 * Messages:
 * - Parent -> Worker:
 *    { type: 'join', clientId, tempName, wagerLamports }
 *    { type: 'input', clientId, tx, ty }
 *    { type: 'eject', clientId }   // W
 *    { type: 'split', clientId }   // Space
 *    { type: 'set_mass', clientId, mass }
 *    { type: 'disconnect', clientId }
 * - Worker -> Parent:
 *    { type: 'tick', now, perClient: { clientId: TickPayload } }
 *    { type: 'join_ack', clientId, you }
 *    { type: 'consumed', by, consumed }
 *
 * This version implements:
 * - Mass decay
 * - Multi-cell gameplay (split) up to CONFIG.MAX_CELLS
 * - Eject pellets (W) with projectile pellets
 */

type PlayerMeta = {
  id: string;
  name: string;
  color: string;
  wagerDollars: number;  // Dollar amount during gameplay
  wagerLamports: number; // Original lamports for conversion back on cashout
  solPriceUsd: number;   // SOL price when they joined (for cashout conversion)
  mergeUnlockAt: number; // when same-owner cells can merge
  lastSplitAt: number;   // global split cooldown timestamp (ms)
  lastDirX: number;      // last valid steer direction (nx)
  lastDirY: number;      // last valid steer direction (ny)
  // Game statistics tracking
  stats: {
    startTime: number;
    maxMass: number;
    killCount: number;
    pelletsEaten: number;
    ejectedMasses: number;
    splits: number;
    wagerStart: number;    // Starting dollar amount
    sizeHistory: Array<{
      timestamp: number; // milliseconds since game start
      mass: number; // total mass at this point
    }>;
  };
  // Track last recorded mass to avoid duplicate entries
  lastRecordedMass: number;
  lastRecordTime: number;
};

type Cell = {
  id: string;        // unique within world; for the primary cell equals ownerId
  ownerId: string;   // connection id
  x: number;
  y: number;
  vx: number;
  vy: number;
  mass: number;
  boostUntil: number; // timestamp (ms) until which speed cap is disabled (throw/recoil)
  steerRampStart: number; // when steering begins ramp-in after an impulse
  steerRampEnd: number;   // when steering is fully restored (weight == 1)
  // Cell merging absorption state
  absorbingInto?: string; // ID of the cell this is being absorbed into
  absorptionStartTime?: number; // When absorption started (ms)
  originalX?: number; // Original position when absorption started
  originalY?: number; // Original position when absorption started
};

type Pellet = {
  id: string;
  x: number;
  y: number;
  mass: number;
  vx: number;
  vy: number;
};

/**
 * Player-ejected mass projectiles (distinct from environmental pellets).
 * Carries color and an optional wager label for rendering.
 */
type Ejected = {
  id: string;
  x: number;
  y: number;
  mass: number;
  vx: number;
  vy: number;
  color?: string;
  wagerDollars?: number;  // Dollar amount for display
  wagerLamports?: number;
};

type Virus = {
  id: string;
  x: number;
  y: number;
  mass: number;
  feedCount: number; // Number of times this virus has been fed
  vx: number; // For movement when spawned from splitting
  vy: number;
  // Absorption tracking for gradual consumption
  beingAbsorbedBy?: string; // Cell ID that is absorbing this virus
  absorptionStartTime?: number; // When absorption started
  absorptionProgress?: number; // 0-1, triggers split at ~0.5
  splitTriggered?: boolean; // Prevents multiple splits from same absorption
};


// Config
const {
  WORLD_SIZE,
  INITIAL_PELLET_COUNT,
  MAX_PELLETS,
  PELLET_MASS,
  PELLET_SPAWN_PER_TICK,
  CELL_SIZE,
  TICK_RATE,

  // gameplay additions
  EJECT_MIN,
  EJECT_MAX,
  EJECT_FRACTION,
  EJECT_BOOST,
  EJECT_LAUNCH_FACTOR,
  SPLIT_BOOST,

  // multi-cell + ejected pellets
  MAX_CELLS,
  EJECT_PELLET_DECEL,
  EJECT_EXTRA_DAMPING,
  // target travel distance for ejected pellets (world units)
  EJECT_TRAVEL_DISTANCE,

  // virus config
  INITIAL_VIRUS_COUNT,
  MAX_VIRUSES,
  VIRUS_MASS,
  VIRUS_FEED_REQUIREMENT,
  VIRUS_SPLIT_MASS_THRESHOLD,
  VIRUS_SPAWN_PER_TICK,

} = CONFIG;

// Medium players can pass over viruses without absorbing (creates uncertainty)
const VIRUS_PASSOVER_MASS_THRESHOLD = VIRUS_SPLIT_MASS_THRESHOLD * 0.7; // 70% of split threshold

// State
const players = new Map<string, PlayerMeta>();
const inputs = new Map<string, { tx: number; ty: number }>();

const cells = new Map<string, Cell>();               // cellId -> Cell
const ownerCells = new Map<string, Set<string>>();   // ownerId -> cellIds

const pellets = new Map<string, Pellet>();
const pelletGrid = new Map<string, Set<string>>();
const pelletCellOf = new Map<string, string>();

// Player-ejected masses (distinct from environmental pellets)
const ejected = new Map<string, Ejected>();

// Viruses
const viruses = new Map<string, Virus>();
const virusGrid = new Map<string, Set<string>>();
const virusCellOf = new Map<string, string>();


// Cashout hold tracking (authoritative)
// - cashoutHoldStartAt: when the player started holding Q (ms since epoch)
// - cashoutPending: once progress hits 1.0 we mark pending to avoid repeated triggers
const cashoutHoldStartAt = new Map<string, number>();
const cashoutPending = new Map<string, boolean>();

// Split/physics constants
const SPLIT_COOLDOWN_MS = 400;
const MERGE_LOCK_MS = 2000;
// Smooth cell drag (per-second multiplicative decay factor)
const CELL_DRAG_DECAY = 0.88;

// Cell merging absorption constants
const ABSORPTION_PHASE_MS = 800; // Duration of visual absorption before final merge
const ABSORPTION_THRESHOLD_DISTANCE = 8; // Distance threshold to complete absorption

// Virus absorption is now immediate when containment condition is met

// Track which cells were spawned this tick
const spawnFrameByCell = new Map<string, number>();

// Temporary grace windows that allow exceeding normal max-speed cap after impulses
const SPLIT_THROW_GRACE_MS = 500;
const RECOIL_GRACE_MS = 300;

// Steering ramp after high-speed impulses (lets fragments carry on before target reasserts)
const SPLIT_CHILD_STEER_RAMP_MS = 600;
const SPLIT_PARENT_STEER_RAMP_MS = 250;
 
 
// Utility
function cellKeyFor(x: number, y: number) {
  const cx = Math.floor(x / CELL_SIZE);
  const cy = Math.floor(y / CELL_SIZE);
  return `${cx},${cy}`;
}

function massToRadius(m: number) {
  return sharedMassToRadius(m);
}

function clamp(n: number, lo: number, hi: number) {
  return Math.max(lo, Math.min(hi, n));
}

function clampWorld(x: number, y: number) {
  return {
    x: Math.max(-WORLD_SIZE / 2, Math.min(WORLD_SIZE / 2, x)),
    y: Math.max(-WORLD_SIZE / 2, Math.min(WORLD_SIZE / 2, y)),
  };
}

function randPosInWorld() {
  return {
    x: (Math.random() - 0.5) * WORLD_SIZE,
    y: (Math.random() - 0.5) * WORLD_SIZE,
  };
}
// Player color palette and picker (hex colors to work with client darken logic)
const PLAYER_COLORS = [
  "#ef4444", "#f97316", "#facc15", "#84cc16", "#22c55e",
  "#10b981", "#06b6d4", "#3b82f6", "#60a5fa", "#818cf8",
  "#a78bfa", "#c084fc", "#e879f9", "#f472b6", "#fb7185"
];

function randPlayerColor() {
  return PLAYER_COLORS[Math.floor(Math.random() * PLAYER_COLORS.length)];
}

function ensureOwnerSet(ownerId: string) {
  if (!ownerCells.has(ownerId)) ownerCells.set(ownerId, new Set());
}

function nextCellId(ownerId: string) {
  // Primary cell should keep ownerId if available
  if (!ownerCells.get(ownerId) || (ownerCells.get(ownerId)!.size === 0 && !cells.has(ownerId))) {
    return ownerId;
  }
  let i = (ownerCells.get(ownerId)?.size ?? 0);
  while (true) {
    const id = `${ownerId}#${i}`;
    if (!cells.has(id)) return id;
    i++;
  }
}

function addCell(ownerId: string, mass: number, x: number, y: number, vx = 0, vy = 0, id?: string) {
  ensureOwnerSet(ownerId);
  const cid = id ?? nextCellId(ownerId);
  const c: Cell = { id: cid, ownerId, x, y, vx, vy, mass, boostUntil: 0, steerRampStart: 0, steerRampEnd: 0 };
  cells.set(cid, c);
  ownerCells.get(ownerId)!.add(cid);
  return c;
}

function removeCell(cellId: string) {
  const c = cells.get(cellId);
  if (!c) return;
  cells.delete(cellId);
  ownerCells.get(c.ownerId)?.delete(cellId);
}

/**
 * Ensure a cell continues to use the owner's primary id (ownerId) so clients
 * that follow youId === ownerId don't lose camera lock after merges.
 */
function rekeyCellId(cell: Cell, newId: string) {
  if (cell.id === newId) return;
  const ownerId = cell.ownerId;
  
  // CRITICAL FIX: Properly handle existing cells with the target ID
  // This was causing mass loss during merging when the surviving cell
  // was being rekeyed to the owner's primary ID
  const existingCell = cells.get(newId);
  if (existingCell && existingCell !== cell) {
    // Store original masses before merging for correct weighted averages
    const cellOriginalMass = cell.mass;
    const existingMass = existingCell.mass;
    const totalMass = cellOriginalMass + existingMass;
    
    // Merge positions and velocities weighted by original masses
    const wx = (cell.x * cellOriginalMass + existingCell.x * existingMass) / totalMass;
    const wy = (cell.y * cellOriginalMass + existingCell.y * existingMass) / totalMass;
    const wvx = (cell.vx * cellOriginalMass + existingCell.vx * existingMass) / totalMass;
    const wvy = (cell.vy * cellOriginalMass + existingCell.vy * existingMass) / totalMass;
    
    // Update cell properties
    cell.mass = totalMass;
    cell.x = wx;
    cell.y = wy;
    cell.vx = wvx;
    cell.vy = wvy;
    
    // CRITICAL: Remove the existing cell from both maps to prevent mass duplication
    cells.delete(newId);
    ownerCells.get(ownerId)?.delete(newId);
  }
  
  // Delete old mapping and set new
  cells.delete(cell.id);
  ownerCells.get(ownerId)?.delete(cell.id);
  cell.id = newId;
  cells.set(newId, cell);
  ownerCells.get(ownerId)?.add(newId);
}

function ownerCellCount(ownerId: string) {
  return ownerCells.get(ownerId)?.size ?? 0;
}

function getOwnerCells(ownerId: string) {
  const ids = ownerCells.get(ownerId);
  if (!ids) return [] as Cell[];
  return Array.from(ids).map((id) => cells.get(id)!).filter(Boolean);
}

function getLargestCell(ownerId: string) {
  let best: Cell | null = null;
  for (const c of getOwnerCells(ownerId)) {
    if (!best || c.mass > best.mass) best = c;
  }
  return best;
}

function setMergeCooldown(ownerId: string, now: number) {
  const meta = players.get(ownerId);
  if (meta) meta.mergeUnlockAt = now + MERGE_LOCK_MS;
}

function addPellet(id: string, x: number, y: number, mass: number, vx = 0, vy = 0) {
  pellets.set(id, { id, x, y, mass, vx, vy });
  const key = cellKeyFor(x, y);
  pelletCellOf.set(id, key);
  let set = pelletGrid.get(key);
  if (!set) {
    set = new Set<string>();
    pelletGrid.set(key, set);
  }
  set.add(id);
}

function movePelletCell(id: string, oldX: number, oldY: number, newX: number, newY: number) {
  const oldKey = pelletCellOf.get(id);
  const newKey = cellKeyFor(newX, newY);
  if (oldKey === newKey) return;
  if (oldKey) {
    const set = pelletGrid.get(oldKey);
    if (set) {
      set.delete(id);
      if (set.size === 0) pelletGrid.delete(oldKey);
    }
  }
  pelletCellOf.set(id, newKey);
  let set = pelletGrid.get(newKey);
  if (!set) {
    set = new Set<string>();
    pelletGrid.set(newKey, set);
  }
  set.add(id);
}

function removePellet(id: string) {
  const cell = pelletCellOf.get(id);
  if (cell) {
    const set = pelletGrid.get(cell);
    if (set) {
      set.delete(id);
      if (set.size === 0) pelletGrid.delete(cell);
    }
    pelletCellOf.delete(id);
  }
  pellets.delete(id);
}

// Virus helper functions
function addVirus(id: string, x: number, y: number, mass: number, vx = 0, vy = 0, feedCount = 0) {
  viruses.set(id, { id, x, y, mass, feedCount, vx, vy });
  const key = cellKeyFor(x, y);
  virusCellOf.set(id, key);
  let set = virusGrid.get(key);
  if (!set) {
    set = new Set<string>();
    virusGrid.set(key, set);
  }
  set.add(id);
}

function moveVirusCell(id: string, oldX: number, oldY: number, newX: number, newY: number) {
  const oldKey = virusCellOf.get(id);
  const newKey = cellKeyFor(newX, newY);
  if (oldKey === newKey) return;
  if (oldKey) {
    const set = virusGrid.get(oldKey);
    if (set) {
      set.delete(id);
      if (set.size === 0) virusGrid.delete(oldKey);
    }
  }
  virusCellOf.set(id, newKey);
  let set = virusGrid.get(newKey);
  if (!set) {
    set = new Set<string>();
    virusGrid.set(newKey, set);
  }
  set.add(id);
}

function removeVirus(id: string) {
  const cell = virusCellOf.get(id);
  if (cell) {
    const set = virusGrid.get(cell);
    if (set) {
      set.delete(id);
      if (set.size === 0) virusGrid.delete(cell);
    }
    virusCellOf.delete(id);
  }
  viruses.delete(id);
}


// Initial pellets
for (let i = 0; i < INITIAL_PELLET_COUNT; i++) {
  const id = crypto.randomUUID();
  const pos = randPosInWorld();
  addPellet(id, pos.x, pos.y, PELLET_MASS, 0, 0);
}

// Initial viruses
for (let i = 0; i < INITIAL_VIRUS_COUNT; i++) {
  const id = crypto.randomUUID();
  const pos = randPosInWorld();
  addVirus(id, pos.x, pos.y, VIRUS_MASS, 0, 0, 0);
}

// Join/Input handlers
parentPort?.on("message", (msg: any) => {
  try {
    const { type } = msg;
    if (type === "join") {
      const { clientId, tempName, wagerLamports, wagerDollars, solPriceUsd } = msg;
      const now = Date.now();
      const startingWagerLamports = wagerLamports ?? 0;
      const startingWagerDollars = wagerDollars ?? 0;
      const solPrice = solPriceUsd ?? 50; // Fallback price
      const p: PlayerMeta = {
        id: clientId,
        name: tempName ?? "player",
        color: randPlayerColor(),
        wagerDollars: startingWagerDollars,
        wagerLamports: startingWagerLamports,
        solPriceUsd: solPrice,
        mergeUnlockAt: 0,
        lastSplitAt: 0,
        lastDirX: 1,
        lastDirY: 0,
        stats: {
          startTime: now,
          maxMass: 20, // Starting mass
          killCount: 0,
          pelletsEaten: 0,
          ejectedMasses: 0,
          splits: 0,
          wagerStart: startingWagerDollars,
          sizeHistory: [{ timestamp: 0, mass: 20 }], // Initial size entry
        },
        lastRecordedMass: 20,
        lastRecordTime: now,
      };
      players.set(clientId, p);
      // Spawn initial cell for this owner
      const pos = randPosInWorld();
      addCell(clientId, 20, pos.x, pos.y, 0, 0, clientId);
      inputs.set(clientId, { tx: pos.x, ty: pos.y });
      parentPort?.postMessage({
        type: "join_ack",
        clientId,
        you: { id: clientId, x: pos.x, y: pos.y, mass: 20, color: p.color },
      });
    } else if (type === "input") {
      const { clientId, tx, ty } = msg;
      if (!players.has(clientId)) return;
      inputs.set(clientId, { tx, ty });
    } else if (type === "eject") {
      const { clientId } = msg;
      const meta = players.get(clientId);
      if (!meta) return;
  
      // Eject from ALL of the player's cells (fragments)
      const all = getOwnerCells(clientId);
      if (all.length === 0) return;
  
      // Prevent eject when player's total mass is 20 (initial spawn/minimum)
      const totalMass = all.reduce((s, c) => s + c.mass, 0);
      if (totalMass <= 20) return;
  
      const inp = inputs.get(clientId);

      for (const src of all) {
        // Compute ejection mass per cell
        const ejectMass = Math.max(EJECT_MIN, Math.min(EJECT_MAX, src.mass * EJECT_FRACTION));
        if (src.mass <= ejectMass + 5) continue;

        // Direction toward input target; fallback to lastDir; then velocity; then random
        let nx = 0, ny = 0;
        if (inp) {
          const dx = inp.tx - src.x;
          const dy = inp.ty - src.y;
          const l = Math.hypot(dx, dy);
          if (l > 1e-4) {
            nx = dx / l; ny = dy / l;
            meta.lastDirX = nx; meta.lastDirY = ny;
          }
        }
        if (nx === 0 && ny === 0) {
          const ld = Math.hypot(meta.lastDirX || 0, meta.lastDirY || 0);
          if (ld > 1e-6) { nx = (meta.lastDirX || 0) / ld; ny = (meta.lastDirY || 0) / ld; }
        }
        if (nx === 0 && ny === 0) {
          const lv = Math.hypot(src.vx, src.vy) || 1;
          nx = src.vx / lv; ny = src.vy / lv;
        }
        if (nx === 0 && ny === 0) {
          const ang = Math.random() * Math.PI * 2;
          nx = Math.cos(ang); ny = Math.sin(ang);
        }

          // Deduct mass on this cell and spawn an ejected mass projectile
          src.mass = Math.max(5, src.mass - ejectMass);

          // Spawn outside the player's collision radius to avoid instant self-consume
          const rParent = massToRadius(src.mass);
          const spawnOff = rParent + EJECT_BOOST + 6;
          const spawnX = src.x + nx * spawnOff;
          const spawnY = src.y + ny * spawnOff;
          const id = crypto.randomUUID();

          // Deduct a fixed $0.05 per ejected blob (dollars), attach to this ejected mass
          let wagerDeductDollars = 0;
          if (typeof meta.wagerDollars === "number" && meta.wagerDollars > 0) {
            // Fixed $0.05 deduction per eject
            const ejectCostDollars = 0.05;
            wagerDeductDollars = Math.min(meta.wagerDollars, ejectCostDollars);
            meta.wagerDollars = Math.max(0, meta.wagerDollars - wagerDeductDollars);
          }

          // Compute initial launch speed from desired travel distance so ejection range
          // is independent of eject mass. The ejected projectile velocity decays
          // multiplicatively each second by (EJECT_PELLET_DECEL * EJECT_EXTRA_DAMPING).
          // For exponential decay v(t) = v0 * decay^t, the total travel distance
          // (integral 0..inf) = v0 / (-ln(decay)). Therefore v0 = D * (-ln(decay)).
          const decay = Math.max(1e-6, EJECT_PELLET_DECEL * EJECT_EXTRA_DAMPING);
          const lnInv = -Math.log(decay);
          let v0: number;
          if (lnInv > 1e-6) {
            // EJECT_TRAVEL_DISTANCE is the tuned distance (world units).
            // EJECT_LAUNCH_FACTOR remains a global tuning multiplier (0..1).
            const vTarget = EJECT_TRAVEL_DISTANCE * lnInv;
            v0 = vTarget * EJECT_LAUNCH_FACTOR;
            // Keep within reasonable practical bounds to avoid extremes
            v0 = clamp(v0, 120, 3000);
          } else {
            // Fallback: preserve previous split-like mass-scaled behavior
            const vSplitLike = clamp(950 / Math.max(1e-6, Math.sqrt(ejectMass)), 450, 1200);
            v0 = vSplitLike * EJECT_LAUNCH_FACTOR;
          }
          ejected.set(id, {
            id,
            x: spawnX,
            y: spawnY,
            mass: ejectMass,
            vx: nx * v0,
            vy: ny * v0,
            color: meta?.color,
            wagerDollars: wagerDeductDollars,
          });
          
          // Track ejected mass statistics
          meta.stats.ejectedMasses++;
      }
    } else if (type === "split") {
      const { clientId } = msg;
      const meta = players.get(clientId);
      if (!meta) return;
      const now = Date.now();
  
      // Global split cooldown
      if (now - (meta.lastSplitAt ?? 0) < SPLIT_COOLDOWN_MS) return;
  
      // Collect eligible cells (mass >= 20 and would produce new fragment mass >= 10)
      const all = getOwnerCells(clientId);
      if (all.length === 0) return;
  
      // Prevent split when player's total mass is 20 (initial spawn/minimum)
      const totalMass = all.reduce((s, c) => s + c.mass, 0);
      if (totalMass <= 20) return;
  
      // Sort largest first
      all.sort((a, b) => b.mass - a.mass);

      let capacity = Math.max(0, MAX_CELLS - ownerCellCount(clientId));
      if (capacity <= 0) return;

      let performed = 0;

      for (const src of all) {
        if (capacity <= 0) break;
        if (src.mass < 20) continue; // eligibility

        // Store original mass before splitting to ensure conservation
        const originalMass = src.mass;
        const newMass = Math.floor(originalMass * 0.5);
        if (newMass < 10) continue; // would create too small fragment

        // Movement direction toward cursor; fallback to last valid direction; then velocity; then random
        let nx = 0, ny = 0;
        const inp = inputs.get(clientId);
        if (inp) {
          const dx = inp.tx - src.x;
          const dy = inp.ty - src.y;
          const l = Math.hypot(dx, dy);
          if (l > 1e-4) {
            nx = dx / l; ny = dy / l;
            meta.lastDirX = nx; meta.lastDirY = ny;
          }
        }
        if (nx === 0 && ny === 0) {
          const ld = Math.hypot(meta.lastDirX || 0, meta.lastDirY || 0);
          if (ld > 1e-6) { nx = (meta.lastDirX || 0) / ld; ny = (meta.lastDirY || 0) / ld; }
        }
        if (nx === 0 && ny === 0) {
          const lv = Math.hypot(src.vx, src.vy);
          if (lv > 1e-6) { nx = src.vx / lv; ny = src.vy / lv; }
        }
        if (nx === 0 && ny === 0) {
          const ang = Math.random() * Math.PI * 2;
          nx = Math.cos(ang); ny = Math.sin(ang);
        }

        // Compute spawn offset to avoid overlaps (use original mass for parent radius calculation)
        const rParent = massToRadius(originalMass);
        const rNew = massToRadius(newMass);
        const spawnOff = rParent + rNew + 2;

        const spawnX = src.x + nx * spawnOff;
        const spawnY = src.y + ny * spawnOff;
        const clamped = clampWorld(spawnX, spawnY);

        // Apply mass split - both cells get half the original mass
        src.mass = newMass;

        // Initial velocities - reduced for shorter travel distance
        const v0 = clamp(1200 / Math.sqrt(newMass), 500, 1600);
        const recoil = 0.35 * v0;

        // Parent recoil
        src.vx -= nx * recoil;
        src.vy -= ny * recoil;
        // Allow recoil to exceed normal cap briefly
        src.boostUntil = Math.max(src.boostUntil || 0, now + RECOIL_GRACE_MS);
        // Temporarily ramp-in steering so recoil momentum carries
        src.steerRampStart = now;
        src.steerRampEnd = now + SPLIT_PARENT_STEER_RAMP_MS;
 
        // Spawn new cell with outward launch
        const newCell = addCell(clientId, newMass, clamped.x, clamped.y, nx * v0, ny * v0);
        // Shorter grace period for reduced travel distance
        const dynamicGracePeriod = Math.min(1000, SPLIT_THROW_GRACE_MS + (v0 / 4));
        newCell.boostUntil = now + dynamicGracePeriod;
        // Temporarily ramp-in steering so it carries past the cursor before steering fully reasserts
        newCell.steerRampStart = now;
        newCell.steerRampEnd = now + SPLIT_CHILD_STEER_RAMP_MS;
        spawnFrameByCell.set(newCell.id, tickCounter);
 
        performed++;
        capacity--;
      }

      if (performed > 0) {
        setMergeCooldown(clientId, now);
        meta.lastSplitAt = now;
        // Track split statistics
        meta.stats.splits += performed;
      }
    } else if (type === "cashout_start") {
      const { clientId } = msg;
      if (!players.has(clientId)) return;
      // If already pending (ready sent), ignore until server applies or client cancels before ready
      if (cashoutPending.get(clientId)) return;
      if (!cashoutHoldStartAt.has(clientId)) {
        cashoutHoldStartAt.set(clientId, Date.now());
      }
    } else if (type === "cashout_cancel") {
      const { clientId } = msg;
      // Cancel current hold if not already dispatched
      if (!cashoutPending.get(clientId)) {
        cashoutHoldStartAt.delete(clientId);
      }
    } else if (type === "apply_cashout") {
      const { clientId, amountDollars } = msg as { clientId: string; amountDollars?: number };
      const meta = players.get(clientId);
      let cashedOutAmountLamports = 0;
      let cashedOutAmountDollars = 0;
      let finalStats: any = null;
      if (meta) {
        const amtDollars = Math.max(0, Number(amountDollars ?? meta.wagerDollars ?? 0));
        const curDollars = Math.max(0, Number(meta.wagerDollars ?? 0));
        const actualCashoutDollars = Math.min(amtDollars, curDollars);
        meta.wagerDollars = Math.max(0, curDollars - actualCashoutDollars);
        cashedOutAmountDollars = actualCashoutDollars;
        // Convert dollars back to lamports using the stored SOL price
        cashedOutAmountLamports = Math.floor((actualCashoutDollars / meta.solPriceUsd) * 1_000_000_000);
        
        // Calculate final statistics
        const currentMass = getOwnerCells(clientId).reduce((s, c) => s + c.mass, 0);
        const timeAlive = Date.now() - meta.stats.startTime;
        
        // Record final size entry
        recordSizeHistory(clientId, Date.now());
        
        finalStats = {
          timeAlive,
          maxMass: Math.max(meta.stats.maxMass, currentMass),
          killCount: meta.stats.killCount,
          pelletsEaten: meta.stats.pelletsEaten,
          ejectedMasses: meta.stats.ejectedMasses,
          splits: meta.stats.splits,
          startTime: meta.stats.startTime,
          endTime: Date.now(),
          finalMass: currentMass,
          wagerStart: meta.stats.wagerStart,
          wagerEnd: meta.wagerDollars,
          gameResult: 'cashout' as const,
          playerColor: meta.color,
          sizeHistory: meta.stats.sizeHistory,
        };
      }
      // Reset hold state for this client
      cashoutHoldStartAt.delete(clientId);
      cashoutPending.delete(clientId);
      
      // Remove player from the game and send them back to lobby
      // Remove all cells for this owner
      for (const cid of Array.from(ownerCells.get(clientId) ?? [])) removeCell(cid);
      ownerCells.delete(clientId);
      players.delete(clientId);
      inputs.delete(clientId);
      
      // Notify parent that player should be disconnected and returned to lobby
      parentPort?.postMessage({
        type: "player_cashed_out",
        clientId,
        amountLamports: cashedOutAmountLamports,
        amountDollars: cashedOutAmountDollars,
        finalStats
      });
    } else if (type === "cashout_reset") {
      const { clientId } = msg;
      cashoutHoldStartAt.delete(clientId);
      cashoutPending.delete(clientId);
    } else if (type === "set_mass") {
      const { clientId, mass } = msg;
      const c = getLargestCell(clientId);
      if (!c) return;
      if (typeof mass === "number" && Number.isFinite(mass)) {
        c.mass = Math.max(5, mass);
      }
    } else if (type === "add_balance") {
      const { clientId, lamports, dollars } = msg;
      const meta = players.get(clientId);
      if (!meta) return;
      if (typeof dollars === "number" && Number.isFinite(dollars) && dollars > 0) {
        meta.wagerDollars = (meta.wagerDollars || 0) + dollars;
        console.log(`[worker] added ${dollars} dollars to player ${clientId}, new balance: ${meta.wagerDollars}`);
      } else if (typeof lamports === "number" && Number.isFinite(lamports) && lamports > 0) {
        // Convert lamports to dollars for backward compatibility
        const dollarsToAdd = (lamports / 1_000_000_000) * meta.solPriceUsd;
        meta.wagerDollars = (meta.wagerDollars || 0) + dollarsToAdd;
        console.log(`[worker] added ${lamports} lamports (${dollarsToAdd} dollars) to player ${clientId}, new balance: ${meta.wagerDollars}`);
      }
    } else if (type === "disconnect") {
      const { clientId } = msg;
      const meta = players.get(clientId);
      let finalStats: any = null;
      
      if (meta) {
        // Calculate final statistics for disconnect
        const currentMass = getOwnerCells(clientId).reduce((s, c) => s + c.mass, 0);
        const timeAlive = Date.now() - meta.stats.startTime;
        
        // Record final size entry
        recordSizeHistory(clientId, Date.now());
        
        finalStats = {
          timeAlive,
          maxMass: Math.max(meta.stats.maxMass, currentMass),
          killCount: meta.stats.killCount,
          pelletsEaten: meta.stats.pelletsEaten,
          ejectedMasses: meta.stats.ejectedMasses,
          splits: meta.stats.splits,
          startTime: meta.stats.startTime,
          endTime: Date.now(),
          finalMass: currentMass,
          wagerStart: meta.stats.wagerStart,
          wagerEnd: meta.wagerDollars,
          gameResult: 'disconnect' as const,
          playerColor: meta.color,
          sizeHistory: meta.stats.sizeHistory,
        };
      }
      
      // Remove all cells for this owner
      for (const cid of Array.from(ownerCells.get(clientId) ?? [])) removeCell(cid);
      ownerCells.delete(clientId);
      players.delete(clientId);
      inputs.delete(clientId);
      // Cleanup cashout state
      cashoutHoldStartAt.delete(clientId);
      cashoutPending.delete(clientId);
      
      // Notify parent about disconnect with final stats
      if (finalStats) {
        parentPort?.postMessage({
          type: "player_disconnected",
          clientId,
          finalStats
        });
      }
    }
  } catch {
    // ignore malformed messages
  }
});

// Helper: split a cell into N fragments without mass loss.
function splitCellIntoFragments(src: Cell, requestedPieces: number) {
  const ownerId = src.ownerId;
  const existing = ownerCellCount(ownerId);
  const capacity = Math.max(0, MAX_CELLS - existing + 1); // +1 because src will be replaced by one fragment
  let pieces = Math.max(2, Math.min(requestedPieces, capacity));
  // Ensure min mass per piece
  const minMass = 5;
  while (pieces > 2 && src.mass / pieces < minMass) pieces--;

  if (pieces < 2) return;

  // Store original mass to ensure conservation
  const originalMass = src.mass;
  
  // Create radial fragments; keep largest fragment in-place and reuse src.id for continuity
  const angle0 = Math.random() * Math.PI * 2;
  const baseSpeed = 260;
  const rOff = SPLIT_BOOST + Math.sqrt(src.mass);
  
  // Calculate mass distribution using floor to always round down
  const baseMass = Math.floor(originalMass / pieces);
  const frags: { m: number; ang: number }[] = [];
  let totalAssigned = 0;

  for (let i = 0; i < pieces; i++) {
    const ang = angle0 + (i / pieces) * Math.PI * 2;
    let m;
    if (i === pieces - 1) {
      // Last fragment gets the remainder to ensure exact mass conservation
      m = originalMass - totalAssigned;
    } else {
      m = baseMass;
      totalAssigned += m;
    }
    frags.push({ m, ang });
  }

  // Replace src as one fragment (use the first one)
  const f0 = frags[0];
  src.mass = f0.m;
  src.vx += Math.cos(f0.ang) * baseSpeed;
  src.vy += Math.sin(f0.ang) * baseSpeed;
  src.x += Math.cos(f0.ang) * rOff;
  src.y += Math.sin(f0.ang) * rOff;

  // Spawn remaining fragments
  for (let i = 1; i < frags.length; i++) {
    const f = frags[i];
    const nx = Math.cos(f.ang);
    const ny = Math.sin(f.ang);
    addCell(ownerId, f.m, src.x + nx * rOff, src.y + ny * rOff, nx * baseSpeed, ny * baseSpeed);
  }

  setMergeCooldown(ownerId, Date.now());
}

// Helper function to record size history
function recordSizeHistory(ownerId: string, now: number) {
  const meta = players.get(ownerId);
  if (!meta) return;
  
  const currentMass = getOwnerCells(ownerId).reduce((s, c) => s + c.mass, 0);
  const timeSinceStart = now - meta.stats.startTime;
  
  // Only record if mass changed significantly (>5%) or enough time has passed (>2 seconds)
  const massChange = Math.abs(currentMass - meta.lastRecordedMass) / Math.max(1, meta.lastRecordedMass);
  const timeSinceLastRecord = now - meta.lastRecordTime;
  
  if (massChange > 0.05 || timeSinceLastRecord > 2000) {
    meta.stats.sizeHistory.push({
      timestamp: timeSinceStart,
      mass: currentMass
    });
    meta.lastRecordedMass = currentMass;
    meta.lastRecordTime = now;
    
    // Limit history to prevent excessive data (keep last 200 points)
    if (meta.stats.sizeHistory.length > 200) {
      meta.stats.sizeHistory = meta.stats.sizeHistory.slice(-200);
    }
  }
}

/**
* Resolve overlaps between same-owner cells so they press against each other instead of clipping.
* Simple position-based relaxation using circle radii.
* Skip cells that are currently absorbing to allow smooth merging.
*/
function resolveSameOwnerOverlaps(iterations = 2) {
 for (let it = 0; it < iterations; it++) {
   for (const [ownerId, ids] of ownerCells.entries()) {
     const list: Cell[] = [];
     for (const id of ids) {
       const c = cells.get(id);
       if (c) list.push(c);
     }
     const n = list.length;
     for (let i = 0; i < n; i++) {
       for (let j = i + 1; j < n; j++) {
         const a = list[i];
         const b = list[j];
         
         // Skip collision resolution if either cell is currently absorbing
         if (a.absorbingInto === b.id || b.absorbingInto === a.id) {
           continue;
         }
         
         const ra = massToRadius(a.mass);
         const rb = massToRadius(b.mass);
         const needed = ra + rb;

         let dx = b.x - a.x;
         let dy = b.y - a.y;
         let dist = Math.hypot(dx, dy);

         if (dist < needed) {
           if (dist < 1e-5) {
             // Avoid NaNs when perfectly overlapping; pick a small random dir
             const ang = Math.random() * Math.PI * 2;
             dx = Math.cos(ang) * 1e-3;
             dy = Math.sin(ang) * 1e-3;
             dist = Math.hypot(dx, dy);
           }
           const nx = dx / dist;
           const ny = dy / dist;
           const push = (needed - dist) * 0.5;

           // Push apart equally
           a.x -= nx * push;
           a.y -= ny * push;
           b.x += nx * push;
           b.y += ny * push;

           // Light velocity separation to reduce sticking (project out approaching component)
           const rvx = b.vx - a.vx;
           const rvy = b.vy - a.vy;
           const vn = rvx * nx + rvy * ny;
           if (vn < 0) {
             const sep = -vn * 0.5;
             a.vx -= nx * sep;
             a.vy -= ny * sep;
             b.vx += nx * sep;
             b.vy += ny * sep;
           }

           // Keep inside world bounds
           const ca = clampWorld(a.x, a.y);
           a.x = ca.x; a.y = ca.y;
           const cb = clampWorld(b.x, b.y);
           b.x = cb.x; b.y = cb.y;
         }
       }
     }
   }
 }
}

/**
 * Resolve collisions between ejected masses to prevent overlapping.
 * Uses elastic collision physics with position-based separation.
 */
function resolveEjectedCollisions(dt: number) {
  const ejectedArray = Array.from(ejected.values());
  const n = ejectedArray.length;
  
  // Early exit if there are too few ejected masses to collide
  if (n < 2) return;
  
  // Check all pairs of ejected masses for collisions
  for (let i = 0; i < n; i++) {
    for (let j = i + 1; j < n; j++) {
      const a = ejectedArray[i];
      const b = ejectedArray[j];
      
      // Calculate distance between centers
      const dx = b.x - a.x;
      const dy = b.y - a.y;
      const distance = Math.hypot(dx, dy);
      
      // Calculate radii using the same formula as the visual rendering
      // From drawEjected.ts: Math.max(20, Math.sqrt(Math.max(0.0001, ej.mass)) * 12.0)
      const radiusA = Math.max(20, Math.sqrt(Math.max(0.0001, a.mass)) * 12.0);
      const radiusB = Math.max(20, Math.sqrt(Math.max(0.0001, b.mass)) * 12.0);
      const minDistance = radiusA + radiusB;
      
      // Check for collision
      if (distance < minDistance && distance > 0.001) {
        // Normalize collision vector
        const nx = dx / distance;
        const ny = dy / distance;
        
        // Calculate overlap and separate positions
        const overlap = minDistance - distance;
        const separationA = overlap * 0.5 * (radiusB / (radiusA + radiusB));
        const separationB = overlap * 0.5 * (radiusA / (radiusA + radiusB));
        
        // Separate positions
        a.x -= nx * separationA;
        a.y -= ny * separationA;
        b.x += nx * separationB;
        b.y += ny * separationB;
        
        // Keep within world bounds
        const clampedA = clampWorld(a.x, a.y);
        a.x = clampedA.x;
        a.y = clampedA.y;
        const clampedB = clampWorld(b.x, b.y);
        b.x = clampedB.x;
        b.y = clampedB.y;
        
        // Calculate relative velocity
        const rvx = b.vx - a.vx;
        const rvy = b.vy - a.vy;
        
        // Calculate relative velocity in collision normal direction
        const velAlongNormal = rvx * nx + rvy * ny;
        
        // Don't resolve if velocities are separating
        if (velAlongNormal > 0) continue;
        
        // Calculate restitution (bounciness) - make it somewhat bouncy but not perfectly elastic
        const restitution = 0.6;
        
        // Calculate impulse scalar
        const impulseScalar = -(1 + restitution) * velAlongNormal;
        
        // Mass-based impulse distribution (heavier objects are less affected)
        const totalMass = a.mass + b.mass;
        const impulseA = impulseScalar * (b.mass / totalMass);
        const impulseB = impulseScalar * (a.mass / totalMass);
        
        // Apply impulse to velocities
        a.vx -= impulseA * nx;
        a.vy -= impulseA * ny;
        b.vx += impulseB * nx;
        b.vy += impulseB * ny;
        
        // Add some damping to prevent excessive bouncing
        const dampingFactor = 0.95;
        a.vx *= dampingFactor;
        a.vy *= dampingFactor;
        b.vx *= dampingFactor;
        b.vy *= dampingFactor;
      }
    }
  }
}

// Simulation loop
let last = Date.now();
let tickCounter = 0;
setInterval(() => {
  const now = Date.now();
  const dtRaw = (now - last) / 1000;
  const dt = Math.min(0.05, Math.max(0.001, dtRaw));
  last = now;

  // Update pellets (projectiles decelerate)
  for (const pel of pellets.values()) {
    if (pel.vx !== 0 || pel.vy !== 0) {
      const oldX = pel.x;
      const oldY = pel.y;
      pel.x += pel.vx * dt;
      pel.y += pel.vy * dt;
      const d = Math.pow(EJECT_PELLET_DECEL, dt);
      pel.vx *= d;
      pel.vy *= d;
      const c = clampWorld(pel.x, pel.y);
      pel.x = c.x; pel.y = c.y;
      movePelletCell(pel.id, oldX, oldY, pel.x, pel.y);
    }
  }

  // Update ejected masses (projectiles decelerate)
  for (const ej of ejected.values()) {
    if (ej.vx !== 0 || ej.vy !== 0) {
      ej.x += ej.vx * dt;
      ej.y += ej.vy * dt;
      // Apply damping (base decel) times extra ejected-specific damping to control travel distance
      const dBase = Math.pow(EJECT_PELLET_DECEL, dt);
      const dExtra = Math.pow(EJECT_EXTRA_DAMPING, dt); // higher -> less extra damping -> travels farther
      const d = dBase * dExtra;
      ej.vx *= d;
      ej.vy *= d;
      // Stop when sufficiently slow to avoid traveling "forever"
      if (Math.hypot(ej.vx, ej.vy) < 30) {
        ej.vx = 0;
        ej.vy = 0;
      }
      const c = clampWorld(ej.x, ej.y);
      ej.x = c.x; ej.y = c.y;
    }
  }

  // Ejected mass collision detection and response
  resolveEjectedCollisions(dt);

  // Update viruses (they can have velocity when spawned from splitting)
  for (const virus of viruses.values()) {
    if (virus.vx !== 0 || virus.vy !== 0) {
      const oldX = virus.x;
      const oldY = virus.y;
      virus.x += virus.vx * dt;
      virus.y += virus.vy * dt;
      // Apply damping to virus movement
      const d = Math.pow(0.85, dt); // Viruses slow down faster than ejected masses
      virus.vx *= d;
      virus.vy *= d;
      // Stop when sufficiently slow
      if (Math.hypot(virus.vx, virus.vy) < 20) {
        virus.vx = 0;
        virus.vy = 0;
      }
      const c = clampWorld(virus.x, virus.y);
      virus.x = c.x;
      virus.y = c.y;
      moveVirusCell(virus.id, oldX, oldY, virus.x, virus.y);
    }
  }


  // Update cells movement and decay
  for (const c of cells.values()) {
    const meta = players.get(c.ownerId);
    const inp = inputs.get(c.ownerId);

    // Determine steering direction toward cursor with fallback
    let nx = 0, ny = 0;
    if (inp) {
      const dx = inp.tx - c.x;
      const dy = inp.ty - c.y;
      const l = Math.hypot(dx, dy);
      if (l > 1e-4) {
        nx = dx / l; ny = dy / l;
        if (meta) { meta.lastDirX = nx; meta.lastDirY = ny; }
      }
    }
    if ((nx === 0 && ny === 0) && meta) {
      const ld = Math.hypot(meta.lastDirX || 0, meta.lastDirY || 0);
      if (ld > 1e-6) { nx = (meta.lastDirX || 0) / ld; ny = (meta.lastDirY || 0) / ld; }
    }
    if (nx === 0 && ny === 0) {
      const lv = Math.hypot(c.vx, c.vy);
      if (lv > 1e-6) { nx = c.vx / lv; ny = c.vy / lv; }
    }

    // Acceleration and speed cap based on individual cell mass (use shared speed curve)
    // Each cell should have its own speed based on its individual mass.
    const sMax = speedForMass(c.mass, CONFIG);
    const accel = sMax * 6.875;

    if (nx !== 0 || ny !== 0) {
      // Skip steering control if this cell is currently being absorbed
      if (!c.absorbingInto) {
        // Ramp-in steering after impulses so thrown fragments can carry on past the mouse
        let steerWeight = 1;
        const rampStart = c.steerRampStart || 0;
        const rampEnd = c.steerRampEnd || 0;
        const rampDur = rampEnd - rampStart;
        if (rampDur > 0 && now < rampEnd) {
          const t = clamp((now - rampStart) / rampDur, 0, 1);
          steerWeight = t;
        }
        c.vx += nx * accel * dt * steerWeight;
        c.vy += ny * accel * dt * steerWeight;
      }
    }

    // Skip physics updates for cells being absorbed (their position is controlled by absorption logic)
    if (c.absorbingInto) {
      continue;
    }
 
    // Apply smooth drag
    const drag = Math.pow(CELL_DRAG_DECAY, dt);
    c.vx *= drag;
    c.vy *= drag;

    // Soft-limit speed (no hard snap). When above sMax after grace, apply extra damping proportional to excess.
    const sp = Math.hypot(c.vx, c.vy);
    if (now >= (c.boostUntil || 0) && sp > sMax) {
      const excess = (sp - sMax) / Math.max(1e-6, sMax);
      const soften = Math.exp(-4.0 * excess * dt); // stronger damping the further above cap
      c.vx *= soften;
      c.vy *= soften;
    }
 
    // Update position (but skip for cells being absorbed as their position is controlled by absorption logic)
    if (!c.absorbingInto) {
      c.x += c.vx * dt;
      c.y += c.vy * dt;
      const clamped = clampWorld(c.x, c.y);
      c.x = clamped.x;
      c.y = clamped.y;
    }

  }

 // Resolve overlaps among same-owner fragments so they don't clip
 // After cooldown, gently attract same-owner fragments toward their center of mass so they can meet and merge
for (const [ownerId, ids] of ownerCells.entries()) {
  const meta = players.get(ownerId);
  if (!meta || now < meta.mergeUnlockAt) continue;
  let sumM = 0, cx = 0, cy = 0;
  const list: Cell[] = [];
  const activeCells: Cell[] = []; // Cells not involved in absorption for COM calculation
  
  for (const id of ids) {
    const c = cells.get(id);
    if (!c) continue;
    list.push(c);
    
    // Only include cells not involved in absorption for center of mass calculation
    if (!c.absorbingInto) {
      let isAbsorbing = false;
      for (const otherId of ids) {
        const other = cells.get(otherId);
        if (other && other.absorbingInto === c.id) {
          isAbsorbing = true;
          break;
        }
      }
      if (!isAbsorbing) {
        activeCells.push(c);
        sumM += c.mass;
        cx += c.x * c.mass;
        cy += c.y * c.mass;
      }
    }
  }
  
  if (activeCells.length <= 1 || sumM <= 0) continue;
  cx /= sumM; cy /= sumM;
  // Apply small cohesion force toward COM (but skip cells that are currently absorbing or being absorbed)
  for (const c of list) {
    // Skip cohesion force if this cell is currently absorbing into another
    if (c.absorbingInto) continue;
    
    // Skip cohesion force if this cell is currently absorbing another cell
    let isAbsorbing = false;
    for (const otherCell of list) {
      if (otherCell.absorbingInto === c.id) {
        isAbsorbing = true;
        break;
      }
    }
    if (isAbsorbing) continue;
    
    const dx = cx - c.x;
    const dy = cy - c.y;
    const dist = Math.hypot(dx, dy) || 1;
    const nx = dx / dist, ny = dy / dist;
    const strength = 40; // tuneable attraction strength
    c.vx += nx * strength * dt;
    c.vy += ny * strength * dt;
  }
}
// Resolve overlaps among same-owner fragments so they don't clip
resolveSameOwnerOverlaps(3);

  // Pellets consumption by cells (spatial hash)
  for (const c of cells.values()) {
    // Skip pickup if player is holding Q to cashout
    if (cashoutHoldStartAt.has(c.ownerId)) {
      continue;
    }
    
    // Skip pickup if this cell is being absorbed
    if (c.absorbingInto) {
      continue;
    }
    
    const radius = massToRadius(c.mass);
    const minCx = Math.floor((c.x - radius) / CELL_SIZE);
    const maxCx = Math.floor((c.x + radius) / CELL_SIZE);
    const minCy = Math.floor((c.y - radius) / CELL_SIZE);
    const maxCy = Math.floor((c.y + radius) / CELL_SIZE);
    for (let cx = minCx; cx <= maxCx; cx++) {
      for (let cy = minCy; cy <= maxCy; cy++) {
        const key = `${cx},${cy}`;
        const set = pelletGrid.get(key);
        if (!set || set.size === 0) continue;
        for (const pelId of Array.from(set)) {
          const pellet = pellets.get(pelId);
          if (!pellet) {
            set.delete(pelId);
            continue;
          }
          const dx = pellet.x - c.x;
          const dy = pellet.y - c.y;
          const dist = Math.hypot(dx, dy);
          if (dist < radius) {
            c.mass += pellet.mass;
            removePellet(pellet.id);
            
            // Track pellet consumption statistics
            const consumerMeta = players.get(c.ownerId);
            if (consumerMeta) {
              consumerMeta.stats.pelletsEaten++;
            }
          }
        }
      }
    }
  }

  // Ejected masses consumption by cells (simple N*M loop; counts are typically small)
  for (const c of cells.values()) {
    // Skip pickup if player is holding Q to cashout
    if (cashoutHoldStartAt.has(c.ownerId)) {
      continue;
    }
    
    // Skip pickup if this cell is being absorbed
    if (c.absorbingInto) {
      continue;
    }
    
    const radius = massToRadius(c.mass);
    for (const [id, ej] of Array.from(ejected.entries())) {
      // Skip if already consumed earlier in this tick
      if (!ejected.has(id)) continue;

      const dx = ej.x - c.x;
      const dy = ej.y - c.y;
      const dist = Math.hypot(dx, dy);
      
      // Use the visual radius for ejected mass consumption to match rendering
      // From drawEjected.ts: Math.max(20, Math.sqrt(Math.max(0.0001, ej.mass)) * 12.0)
      const ejRadius = Math.max(20, Math.sqrt(Math.max(0.0001, ej.mass)) * 12.0);
      
      // Proper circle-overlap check: player radius + ejected radius
      if (dist < radius + ejRadius) {
        // Increase mass
        c.mass += ej.mass;

        // Credit any attached wager value to the consumer's owner meta
        const consumerMeta = players.get(c.ownerId);
        if (consumerMeta && ej.wagerLamports && ej.wagerLamports > 0) {
          consumerMeta.wagerLamports = (consumerMeta.wagerLamports || 0) + ej.wagerLamports;
        }
        if (consumerMeta && ej.wagerDollars && ej.wagerDollars > 0) {
          consumerMeta.wagerDollars = (consumerMeta.wagerDollars || 0) + ej.wagerDollars;
        }

        // Remove the ejected mass
        ejected.delete(id);
      }
    }
  }

  // Helper function to split a player when they absorb a virus
  function splitPlayerFromVirusAbsorption(cell: Cell, originalMass: number) {
    const ownerId = cell.ownerId;
    const existing = ownerCellCount(ownerId);

    // Create 4-8 fragments depending on mass
    const fragmentCount = Math.min(8, Math.max(4, Math.floor(originalMass / 50)));
    const capacity = Math.max(0, MAX_CELLS - existing + 1); // +1 because original cell will be replaced
    const actualFragments = Math.min(fragmentCount, capacity);

    if (actualFragments <= 1) {
      // Can't split, just reduce mass slightly as penalty
      cell.mass = Math.max(10, originalMass * 0.8);
      return;
    }

    // Create random mass distribution with varied fragment sizes
    const fragments: { mass: number; angle: number }[] = [];
    const usedAngles: number[] = [];

    // Generate random mass weights for each fragment
    const massWeights: number[] = [];
    let totalWeight = 0;

    for (let i = 0; i < actualFragments; i++) {
      // Create varied weights - some fragments will be much larger/smaller
      // Use exponential distribution for more dramatic size differences
      let weight;
      const rand = Math.random();

      if (rand < 0.15) {
        // 5% chance for large fragments (1.2-1.8x normal size)
        weight = 1.1 + Math.random() * 0.6;
      } else if (rand < 0.25) {
        // 10% chance for medium fragments (0.7-1.2x normal size)
        weight = 0.5 + Math.random() * 0.5;
      } else {
        // 85% chance for small fragments (0.3-0.7x normal size)
        weight = 0.3 + Math.random() * 0.4;
      }

      massWeights.push(weight);
      totalWeight += weight;
    }

    // Normalize weights and assign masses
    let totalAssigned = 0;

    for (let i = 0; i < actualFragments; i++) {
      // Generate a random angle, ensuring some minimum separation between fragments
      let angle: number;
      let attempts = 0;
      do {
        angle = Math.random() * Math.PI * 2;
        attempts++;
      } while (attempts < 10 && usedAngles.some(usedAngle =>
        Math.abs(angle - usedAngle) < Math.PI / 4 && usedAngles.length > 0
      ));

      usedAngles.push(angle);

      let mass: number;
      if (i === actualFragments - 1) {
        // Last fragment gets remainder to ensure exact mass conservation
        mass = originalMass - totalAssigned;
      } else {
        // Assign mass based on normalized weight
        const normalizedWeight = massWeights[i] / totalWeight;
        mass = originalMass * normalizedWeight;
        totalAssigned += mass;
      }

      // Ensure minimum mass
      mass = Math.max(10, mass);
      fragments.push({ mass, angle });
    }

    // Replace original cell with first fragment
    cell.mass = fragments[0].mass;
    const baseVelocity = 800; // High velocity for dramatic split
    const mainCellVelocity = 400; // Reduced velocity for the main controllable cell
    cell.vx = Math.cos(fragments[0].angle) * mainCellVelocity;
    cell.vy = Math.sin(fragments[0].angle) * mainCellVelocity;
    cell.boostUntil = Date.now() + 500; // Shorter boost duration for main cell
    cell.steerRampStart = Date.now();
    cell.steerRampEnd = Date.now() + 300; // Shorter steering ramp for better control

    // Create additional fragments
    for (let i = 1; i < fragments.length; i++) {
      const fragment = fragments[i];
      const newId = crypto.randomUUID();
      const spawnOffset = massToRadius(fragment.mass) + massToRadius(cell.mass) + 10;
      const spawnX = cell.x + Math.cos(fragment.angle) * spawnOffset;
      const spawnY = cell.y + Math.sin(fragment.angle) * spawnOffset;
      const clamped = clampWorld(spawnX, spawnY);

      const newCell: Cell = {
        id: newId,
        ownerId,
        x: clamped.x,
        y: clamped.y,
        mass: fragment.mass,
        vx: Math.cos(fragment.angle) * baseVelocity,
        vy: Math.sin(fragment.angle) * baseVelocity,
        boostUntil: Date.now() + 1000,
        steerRampStart: Date.now(),
        steerRampEnd: Date.now() + 500,
      };

      addCell(ownerId, newCell.mass, newCell.x, newCell.y, newCell.vx, newCell.vy, newId);
    }

    // Set merge cooldown
    setMergeCooldown(ownerId, Date.now());
  }

  // Virus-player interactions are now handled immediately in the collision detection below
  // No need for gradual absorption progress tracking

  // Virus-player collision detection for starting new absorptions
  for (const c of cells.values()) {
    // Skip if player is holding Q to cashout
    if (cashoutHoldStartAt.has(c.ownerId)) {
      continue;
    }

    // Skip if this cell is being absorbed
    if (c.absorbingInto) {
      continue;
    }

    const radius = massToRadius(c.mass);
    for (const [id, virus] of Array.from(viruses.entries())) {
      // Skip if already consumed earlier in this tick
      if (!viruses.has(id)) continue;

      // Skip if virus is already being absorbed
      if (virus.beingAbsorbedBy) continue;

      const dx = virus.x - c.x;
      const dy = virus.y - c.y;
      const dist = Math.hypot(dx, dy);

      // Virus radius calculation (similar to rendering)
      const virusRadius = Math.max(35, Math.sqrt(Math.max(0.0001, virus.mass)) * 8.0);

      // Three-tier virus interaction system:
      if (c.mass >= VIRUS_SPLIT_MASS_THRESHOLD && dist < radius + virusRadius) {
        // LARGE PLAYERS (mass >= 120): Can absorb viruses and get split
        // Check if they've fully consumed the inner trigger zone
        const innerTriggerRadius = virusRadius * 0.5;
        const playerFullyContainsInner = radius >= (dist + innerTriggerRadius);

        if (playerFullyContainsInner) {
          // Player has fully consumed the inner trigger zone - trigger immediate split
          const originalMass = c.mass;

          // Remove the virus immediately
          removeVirus(id);

          // Split the player into multiple fragments immediately
          splitPlayerFromVirusAbsorption(c, originalMass);

          // Break out of virus loop since this virus is consumed
          break;
        }
        // Large players don't bounce off - they can push through or get absorbed

      } else if (c.mass >= VIRUS_PASSOVER_MASS_THRESHOLD) {
        // MEDIUM PLAYERS (mass 84-119): Pass over viruses without interaction
        // Creates uncertainty - players don't know if they're safe or not
        // No collision, no absorption, no bouncing - just pass over

      }
      // SMALL PLAYERS (mass < 84): Pass through viruses completely (hide underneath)
      // This allows them to hide underneath for protection from larger players
    }
  }

  // Ejected mass-virus interactions (feeding viruses)
  for (const [ejId, ej] of Array.from(ejected.entries())) {
    // Skip if already consumed
    if (!ejected.has(ejId)) continue;

    const ejRadius = Math.max(20, Math.sqrt(Math.max(0.0001, ej.mass)) * 12.0);

    for (const [virusId, virus] of Array.from(viruses.entries())) {
      // Skip if virus already processed
      if (!viruses.has(virusId)) continue;

      const dx = virus.x - ej.x;
      const dy = virus.y - ej.y;
      const dist = Math.hypot(dx, dy);
      const virusRadius = Math.max(35, Math.sqrt(Math.max(0.0001, virus.mass)) * 8.0);

      // Check collision
      if (dist < virusRadius + ejRadius) {
        // Feed the virus
        virus.feedCount++;

        // Bounce the ejected mass off the virus
        const bounceForce = 300;
        const nx = dx / dist;
        const ny = dy / dist;
        ej.vx = -nx * bounceForce;
        ej.vy = -ny * bounceForce;

        // Check if virus should split
        if (virus.feedCount >= VIRUS_FEED_REQUIREMENT) {
          // Spawn new virus in opposite direction of the ejected mass
          const newVirusId = crypto.randomUUID();
          const spawnDistance = virusRadius * 3;
          const spawnX = virus.x + nx * spawnDistance;
          const spawnY = virus.y + ny * spawnDistance;
          const clamped = clampWorld(spawnX, spawnY);

          // Launch new virus with velocity
          const launchSpeed = 400;
          addVirus(newVirusId, clamped.x, clamped.y, VIRUS_MASS, nx * launchSpeed, ny * launchSpeed, 0);

          // Reset original virus feed count
          virus.feedCount = 0;
        }

        break; // Ejected mass can only hit one virus per tick
      }
    }
  }

  // Cell-cell interactions (consumption and merge)
  const allCellIds = Array.from(cells.keys());
  
  // First pass: Clean up orphaned absorption states
  for (const cellId of allCellIds) {
    const cell = cells.get(cellId);
    if (!cell || !cell.absorbingInto) continue;
    
    // Check if the target cell still exists
    const targetCell = cells.get(cell.absorbingInto);
    if (!targetCell) {
      // Target cell no longer exists, clear absorption state
      cell.absorbingInto = undefined;
      cell.absorptionStartTime = undefined;
      cell.originalX = undefined;
      cell.originalY = undefined;
    }
    
    // Check for invalid absorption state (absorbingInto set but no start time)
    if (cell.absorbingInto && !cell.absorptionStartTime) {
      // Invalid state, clear it
      cell.absorbingInto = undefined;
      cell.originalX = undefined;
      cell.originalY = undefined;
    }
    
    // Check for extremely long absorption (potential stuck state)
    if (cell.absorbingInto && cell.absorptionStartTime && (now - cell.absorptionStartTime) > ABSORPTION_PHASE_MS * 3) {
      // Force clear stuck absorption state
      cell.absorbingInto = undefined;
      cell.absorptionStartTime = undefined;
      cell.originalX = undefined;
      cell.originalY = undefined;
    }
  }
  
  for (let i = 0; i < allCellIds.length; i++) {
    const idA = allCellIds[i];
    const a = cells.get(idA);
    if (!a) continue;
    
    // Skip interactions for cells being absorbed
    if (a.absorbingInto) continue;
    
    let cellMerged = false;
    for (let j = 0; j < allCellIds.length; j++) {
      if (i === j) continue;
      const idB = allCellIds[j];
      const b = cells.get(idB);
      if (!b) continue;

      if (a.ownerId === b.ownerId) {
        // Same owner - consider merge only after cooldown
        const meta = players.get(a.ownerId);
        if (meta && now >= meta.mergeUnlockAt) {
          const ra = massToRadius(a.mass);
          const rb = massToRadius(b.mass);
          const dx = a.x - b.x;
          const dy = a.y - b.y;
          const dist = Math.hypot(dx, dy);
          
          // Check if cells are close enough to start or continue merging
          // Use a buffer zone to prevent oscillation between start/cancel
          const mergeDistance = ra + rb + 2;
          const cancelDistance = mergeDistance + 5; // Buffer zone to prevent oscillation
          
          if (dist <= mergeDistance) {
            // Determine which cell is larger (will be the absorber)
            const larger = a.mass >= b.mass ? a : b;
            const smaller = a.mass >= b.mass ? b : a;
            
            // Check if absorption is already in progress
            if (smaller.absorbingInto === larger.id) {
              // Continue absorption process
              const absorptionDuration = now - (smaller.absorptionStartTime || now);
              const absorptionProgress = Math.min(1, absorptionDuration / ABSORPTION_PHASE_MS);
              
              // Safety check: if absorption has been going on too long, force completion
              if (absorptionDuration > ABSORPTION_PHASE_MS * 2) {
                // Force merge completion
                const keepOriginalMass = larger.mass;
                const velocityDamping = 0.3;
                larger.mass += smaller.mass;
                larger.vx = (larger.vx * keepOriginalMass + smaller.vx * smaller.mass * velocityDamping) / larger.mass;
                larger.vy = (larger.vy * keepOriginalMass + smaller.vy * smaller.mass * velocityDamping) / larger.mass;
                
                // Clear absorption state
                smaller.absorbingInto = undefined;
                smaller.absorptionStartTime = undefined;
                smaller.originalX = undefined;
                smaller.originalY = undefined;
                
                const ownerIdMerge = larger.ownerId;
                const dropId = smaller.id;
                removeCell(dropId);
                
                if (larger.id !== ownerIdMerge) {
                  rekeyCellId(larger, ownerIdMerge);
                }
                
                cellMerged = true;
                break;
              }
              
              // Move smaller cell toward larger cell during absorption
              if (smaller.originalX !== undefined && smaller.originalY !== undefined) {
                // Use the larger cell's current position as target so it follows the moving cell
                const targetX = larger.x;
                const targetY = larger.y;
                const moveProgress = 1 - Math.pow(1 - absorptionProgress, 3); // Ease-out cubic
                
                smaller.x = smaller.originalX + (targetX - smaller.originalX) * moveProgress;
                smaller.y = smaller.originalY + (targetY - smaller.originalY) * moveProgress;
                
                // Check if close enough or time exceeded to complete merge
                const currentDist = Math.hypot(smaller.x - larger.x, smaller.y - larger.y);
                if (absorptionProgress >= 1 || currentDist <= ABSORPTION_THRESHOLD_DISTANCE) {
                  // Complete the merge
                  const keepOriginalMass = larger.mass;
                  const dropOriginalMass = smaller.mass;
                  const mt = keepOriginalMass + dropOriginalMass;
                  
                  // Calculate weighted averages using original masses
                  const wx = (larger.x * keepOriginalMass + smaller.x * dropOriginalMass) / mt;
                  const wy = (larger.y * keepOriginalMass + smaller.y * dropOriginalMass) / mt;
                  
                  // For velocity, preserve the larger cell's momentum more to avoid sudden acceleration
                  // Use a dampening factor to reduce the impact of the smaller cell's velocity
                  const velocityDamping = 0.3; // Reduce smaller cell's velocity contribution
                  const wvx = (larger.vx * keepOriginalMass + smaller.vx * dropOriginalMass * velocityDamping) / (keepOriginalMass + dropOriginalMass * velocityDamping);
                  const wvy = (larger.vy * keepOriginalMass + smaller.vy * dropOriginalMass * velocityDamping) / (keepOriginalMass + dropOriginalMass * velocityDamping);

                  larger.mass = mt;
                  larger.x = wx; larger.y = wy;
                  larger.vx = wvx; larger.vy = wvy;

                  // Clear absorption state from the larger cell (no originalX/Y needed anymore)

                  // Remove smaller cell
                  const ownerIdMerge = a.ownerId; // same for both since same-owner branch
                  const dropId = smaller.id;
                  removeCell(dropId);

                  // Ensure the surviving merged cell uses the owner's primary id
                  if (larger.id !== ownerIdMerge) {
                    rekeyCellId(larger, ownerIdMerge);
                  }
                  
                  cellMerged = true;
                  break; // Break inner loop after merge to avoid processing stale references
                }
              }
            } else if (!smaller.absorbingInto) {
              // Start new absorption process
              smaller.absorbingInto = larger.id;
              smaller.absorptionStartTime = now;
              smaller.originalX = smaller.x;
              smaller.originalY = smaller.y;
              
              // No need to store larger cell's position since we follow its current position
              
              // Add visual effects to the larger cell (create dent effect)
              const angle = Math.atan2(smaller.y - larger.y, smaller.x - larger.x);
              // Signal absorption started (this will be picked up by the client for visual effects)
            }
          } else if (dist > cancelDistance) {
            // Cells moved apart beyond buffer zone, cancel any ongoing absorption
            if (b.absorbingInto === a.id) {
              b.absorbingInto = undefined;
              b.absorptionStartTime = undefined;
              b.originalX = undefined;
              b.originalY = undefined;
            }
            if (a.absorbingInto === b.id) {
              a.absorbingInto = undefined;
              a.absorptionStartTime = undefined;
              a.originalX = undefined;
              a.originalY = undefined;
            }
          }
          // If dist is between mergeDistance and cancelDistance, maintain current state
        }
      } else {
        // Different owners - consumption
        if (a.mass <= b.mass * 1.15) continue;
        const dx = a.x - b.x;
        const dy = a.y - b.y;
        const dist = Math.hypot(dx, dy);
        const ra = massToRadius(a.mass);
        const rb = massToRadius(b.mass);
        if (dist < Math.max(1, ra - rb * 0.25)) {
          a.mass += b.mass;
          const victimOwner = b.ownerId;
          removeCell(b.id);
          // If victim has no cells left, end them
          if (ownerCellCount(victimOwner) === 0) {
            // transfer wager to the eater's owner meta
            const eaterOwner = a.ownerId;
            const eaterMeta = players.get(eaterOwner);
            const victimMeta = players.get(victimOwner);
            if (eaterMeta && victimMeta) {
              eaterMeta.wagerLamports += victimMeta.wagerLamports;
              eaterMeta.wagerDollars += victimMeta.wagerDollars;
              victimMeta.wagerLamports = 0;
              victimMeta.wagerDollars = 0;
              // Track kill statistics
              eaterMeta.stats.killCount++;
              
              // Calculate final statistics for consumed player
              const currentMass = getOwnerCells(victimOwner).reduce((s, c) => s + c.mass, 0);
              const timeAlive = Date.now() - victimMeta.stats.startTime;
              
              // Record final size entry
              recordSizeHistory(victimOwner, Date.now());
              
              const finalStats = {
                timeAlive,
                maxMass: Math.max(victimMeta.stats.maxMass, currentMass),
                killCount: victimMeta.stats.killCount,
                pelletsEaten: victimMeta.stats.pelletsEaten,
                ejectedMasses: victimMeta.stats.ejectedMasses,
                splits: victimMeta.stats.splits,
                startTime: victimMeta.stats.startTime,
                endTime: Date.now(),
                finalMass: currentMass,
                wagerStart: victimMeta.stats.wagerStart,
                wagerEnd: victimMeta.wagerLamports,
                gameResult: 'consumed' as const,
                consumedBy: eaterOwner,
                sizeHistory: victimMeta.stats.sizeHistory,
                playerColor: victimMeta.color,
              };
              
              parentPort?.postMessage({
                type: "consumed",
                by: eaterOwner,
                consumed: victimOwner,
                finalStats
              });
            }
            players.delete(victimOwner);
            inputs.delete(victimOwner);
            ownerCells.delete(victimOwner);
            // Cleanup any active cashout hold state for the victim
            cashoutHoldStartAt.delete(victimOwner);
            cashoutPending.delete(victimOwner);
          }
        }
      }
    }
  }

  // Spawn pellets to maintain density
  if (pellets.size < MAX_PELLETS) {
    for (let s = 0; s < PELLET_SPAWN_PER_TICK && pellets.size < MAX_PELLETS; s++) {
      const id = crypto.randomUUID();
      const pos = randPosInWorld();
      addPellet(id, pos.x, pos.y, PELLET_MASS, 0, 0);
    }
  }

  // Spawn viruses to maintain density
  if (viruses.size < MAX_VIRUSES) {
    for (let s = 0; s < VIRUS_SPAWN_PER_TICK && viruses.size < MAX_VIRUSES; s++) {
      const id = crypto.randomUUID();
      const pos = randPosInWorld();
      addVirus(id, pos.x, pos.y, VIRUS_MASS, 0, 0, 0);
    }
  }

  // Build snapshots once per tick
  tickCounter++;
  const includePellets = (tickCounter % 3 === 0);
  const includeEjected = (tickCounter % 2 === 0);
  const includeViruses = (tickCounter % 4 === 0); // Include viruses every 4th tick

  // Authoritative cashout hold progress per owner (0..1). When reaching 1, notify server once.
  const CASHOUT_HOLD_MS = 5000;
  const progressByOwner = new Map<string, number>();
  for (const [ownerId, startAt] of cashoutHoldStartAt.entries()) {
    if (cashoutPending.get(ownerId)) continue;
    const prog = clamp((now - startAt) / CASHOUT_HOLD_MS, 0, 1);
    progressByOwner.set(ownerId, prog);
    if (prog >= 1) {
      cashoutPending.set(ownerId, true);
      const meta = players.get(ownerId);
      const amtDollars = Math.max(0, Number(meta?.wagerDollars ?? 0));
      parentPort?.postMessage({ type: "cashout_ready", clientId: ownerId, amountDollars: amtDollars });
    }
  }

  // Global leaderboard (sum mass across owner's cells) + include wager in lamports
  // Also update max mass statistics and record size history
  const massByOwner = new Map<string, number>();
  for (const meta of players.values()) {
    let sum = 0;
    for (const c of getOwnerCells(meta.id)) sum += c.mass;
    massByOwner.set(meta.id, sum);
    
    // Update max mass if current total is higher
    if (sum > meta.stats.maxMass) {
      meta.stats.maxMass = sum;
    }
    
    // Record size history for this player
    recordSizeHistory(meta.id, now);
  }
  const globalLeaderboard = Array.from(players.values())
    .map((m) => ({
      id: m.id,
      name: m.name,
      mass: massByOwner.get(m.id) ?? 0,
      wagerLamports: m.wagerLamports ?? 0,
      wagerDollars: m.wagerDollars ?? 0,
    }))
    .sort((a, b) => b.mass - a.mass)
    .slice(0, 10);

  // Prepare per-client localized snapshots
  const perClient: Record<string, any> = {};
  for (const meta of players.values()) {
    const clientId = meta.id;
    // Use the primary cell (id===ownerId) to compute view; fallback to largest
    const primary = cells.get(clientId) ?? getLargestCell(clientId);
    const meX = primary?.x ?? 0;
    const meY = primary?.y ?? 0;
    const meMass = (getOwnerCells(clientId).reduce((s, c) => s + c.mass, 0)) || 0;
    const viewRadius = 900 + Math.sqrt(meMass) * 40;
    const vr2 = viewRadius * viewRadius;

    const nearbyPlayers: any[] = [];
    // Divide wager amount across the owner's active fragments so each blob shows its share
    const ownerCellCounts = new Map<string, number>();
    for (const [oid, set] of ownerCells.entries()) {
      ownerCellCounts.set(oid, set?.size ?? 0);
    }
    for (const c of cells.values()) {
      const dx = c.x - meX;
      const dy = c.y - meY;
      const inView = dx * dx + dy * dy <= vr2;
      const isOwn = c.ownerId === clientId; // always include your own cells to preserve camera lock
      if (inView || isOwn) {
        const owner = players.get(c.ownerId);
        const count = Math.max(1, ownerCellCounts.get(c.ownerId) ?? 1);
        const perCellWager = (owner?.wagerLamports ?? 0) / count;
        const perCellWagerDollars = (owner?.wagerDollars ?? 0) / count;

        nearbyPlayers.push({
          id: c.id,
          name: owner?.name ?? "player",
          x: c.x,
          y: c.y,
          mass: c.mass,
          wagerLamports: perCellWager,
          wagerDollars: perCellWagerDollars,
          color: owner?.color,
          // Only show your own hold progress on your blobs
          cashoutHoldProgress: c.ownerId === clientId ? (progressByOwner.get(clientId) ?? undefined) : undefined,
          // Include absorption state for visual effects
          absorbingInto: c.absorbingInto,
          absorptionStartTime: c.absorptionStartTime,
          originalX: c.originalX,
          originalY: c.originalY,
        });
      }
    }

    const payload: any = { t: now, players: nearbyPlayers, worldSize: WORLD_SIZE };

    if (includePellets) {
      const minCx = Math.floor((meX - viewRadius) / CELL_SIZE);
      const maxCx = Math.floor((meX + viewRadius) / CELL_SIZE);
      const minCy = Math.floor((meY - viewRadius) / CELL_SIZE);
      const maxCy = Math.floor((meY + viewRadius) / CELL_SIZE);
      const seen = new Set<string>();
      const pelArr: any[] = [];
      for (let cx = minCx; cx <= maxCx; cx++) {
        for (let cy = minCy; cy <= maxCy; cy++) {
          const key = `${cx},${cy}`;
          const set = pelletGrid.get(key);
          if (!set) continue;
          for (const pid of set) {
            if (seen.has(pid)) continue;
            const pel = pellets.get(pid);
            if (!pel) continue;
            const dx = pel.x - meX;
            const dy = pel.y - meY;
            if (dx * dx + dy * dy <= vr2) {
              seen.add(pid);
              pelArr.push({ id: pel.id, x: pel.x, y: pel.y, mass: pel.mass });
            }
          }
        }
      }
      payload.pellets = pelArr;
    }

    // Include visible ejected masses (player-generated)
    if (includeEjected) {
      const ejArr: any[] = [];
      for (const ej of ejected.values()) {
        const dx = ej.x - meX;
        const dy = ej.y - meY;
        if (dx * dx + dy * dy <= vr2) {
          ejArr.push({ id: ej.id, x: ej.x, y: ej.y, mass: ej.mass, color: ej.color, wagerLamports: ej.wagerLamports, wagerDollars: ej.wagerDollars });
        }
      }
      (payload as any).ejected = ejArr;
    }

    // Include visible viruses
    if (includeViruses) {
      const virusArr: any[] = [];
      for (const virus of viruses.values()) {
        const dx = virus.x - meX;
        const dy = virus.y - meY;
        if (dx * dx + dy * dy <= vr2) {
          virusArr.push({
            id: virus.id,
            x: virus.x,
            y: virus.y,
            mass: virus.mass,
            feedCount: virus.feedCount,
            absorptionProgress: virus.absorptionProgress || 0,
            beingAbsorbedBy: virus.beingAbsorbedBy
          });
        }
      }
      (payload as any).viruses = virusArr;
    }

    payload.leaderboard = globalLeaderboard;
    perClient[clientId] = payload;
  }

  parentPort?.postMessage({ type: "tick", now, perClient });
}, 1000 / TICK_RATE);